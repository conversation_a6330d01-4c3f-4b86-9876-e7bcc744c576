<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="@drawable/quick_panel_bg_right"
    android:padding="8dp">

    <ImageButton
        android:id="@+id/btn_back"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="4dp"
        android:background="@drawable/float_icon_bg"
        android:src="@drawable/touch_arrow_left"
        android:scaleType="centerInside"
        android:contentDescription="返回" />

    <ImageButton
        android:id="@+id/btn_home"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="4dp"
        android:background="@drawable/float_icon_bg"
        android:src="@drawable/touch_home"
        android:scaleType="centerInside"
        android:contentDescription="主页" />

    <ImageButton
        android:id="@+id/btn_recent"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="4dp"
        android:background="@drawable/float_icon_bg"
        android:src="@drawable/touch_tasks"
        android:scaleType="centerInside"
        android:contentDescription="最近任务" />

    <ImageButton
        android:id="@+id/btn_notification"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="4dp"
        android:background="@drawable/float_icon_bg"
        android:src="@drawable/touch_notice"
        android:scaleType="centerInside"
        android:contentDescription="通知栏" />

</LinearLayout>
