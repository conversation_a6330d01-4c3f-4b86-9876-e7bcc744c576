package com.smartcar.easylauncher.core.manager;

import com.smartcar.easylauncher.core.constants.UserConstants;
import com.smartcar.easylauncher.shared.utils.cache.MMKVUtils;

public class UserManager {
    /**
     * 退出登录
     */

    public static void logout() {
        setLogin(false);
        setUserCode("");
        setUserName("");
        setNickName("");
        setUserAvatar("");
        setUserPhone("");
        setUserEmail("");
        setUserInfo("");
        AuthorityManager.setToken("");
    }


    /**
     * 是否登录
     */
    public static void setLogin(boolean isLogin) {
        MMKVUtils.put(UserConstants.IS_LOGIN, isLogin);
    }

    /**
     * 是否登录
     */
    public static boolean isLogin() {
        return MMKVUtils.get(UserConstants.IS_LOGIN, false);
    }

    /**
     * 设置用户编码
     */
    public static void setUserCode(String userCode) {
        MMKVUtils.put(UserConstants.USER_CODE, userCode);
    }

    /**
     * 获取用户编码
     *
     * @return
     */
    public static String getUserCode() {
        return MMKVUtils.get(UserConstants.USER_CODE, "");
    }

    /**
     * 设置用户名称
     */
    public static void setUserName(String userName) {
        MMKVUtils.put(UserConstants.USER_NAME, userName);
    }

    /**
     * 获取用户名称
     *
     * @return
     */
    public static String getUserName() {
        return MMKVUtils.get(UserConstants.USER_NAME, "");
    }

    /**
     * 设置用户昵称
     */
    public static void setNickName(String userNickName) {
        MMKVUtils.put(UserConstants.NICK_NAME, userNickName);
    }

    /**
     * 获取用户昵称
     *
     * @return
     */
    public static String getNickName() {
        return MMKVUtils.get(UserConstants.NICK_NAME, "");
    }


    /**
     * 设置用户ID
     */
    public static void setUserId(Long userId) {
        MMKVUtils.put(UserConstants.USER_ID, userId);
    }

    /**
     * 获取用户ID
     *
     * @return
     */
    public static Long getUserId() {
        return MMKVUtils.get(UserConstants.USER_ID, 999999999L);
    }

    /**
     * 设置用户头像
     */
    public static void setUserAvatar(String userAvatar) {
        MMKVUtils.put(UserConstants.USER_AVATAR, userAvatar);
    }

    /**
     * 获取用户头像
     *
     * @return
     */
    public static String getUserAvatar() {
        return MMKVUtils.get(UserConstants.USER_AVATAR, "");
    }

    /**
     * 设置用户手机号
     */
    public static void setUserPhone(String userPhone) {
        MMKVUtils.put(UserConstants.USER_PHONE, userPhone);
    }

    /**
     * 获取用户手机号
     *
     * @return
     */
    public static String getUserPhone() {
        return MMKVUtils.get(UserConstants.USER_PHONE, "");
    }

    /**
     * 设置用户邮箱
     */
    public static void setUserEmail(String userEmail) {
        MMKVUtils.put(UserConstants.USER_EMAIL, userEmail);
    }

    /**
     * 获取用户邮箱
     *
     * @return
     */
    public static String getUserEmail() {
        return MMKVUtils.get(UserConstants.USER_EMAIL, "");
    }

    /**
     * 用户信息数据
     */
    public static void setUserInfo(String userInfo) {
        MMKVUtils.put(UserConstants.USER_INFO, userInfo);
    }

    /**
     * 获取用户信息数据
     *
     * @return
     */
    public static String getUserInfo() {
        return MMKVUtils.get(UserConstants.USER_INFO, "");
    }

    /**
     * 用户组
     */
    public static void setGroup(String group) {
        MMKVUtils.put(UserConstants.USER_GROUP, group);
    }

    /**
     * 获取用户组
     *
     * @return
     */
    public static String getGroup() {
        return MMKVUtils.get(UserConstants.USER_GROUP, "");
    }

}
