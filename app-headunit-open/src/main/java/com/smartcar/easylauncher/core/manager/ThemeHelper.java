package com.smartcar.easylauncher.core.manager;

import com.smartcar.easylauncher.infrastructure.skin.SkinManager;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.location.SunRiseSet;
import com.smartcar.easylauncher.shared.utils.time.TimeTools;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;

/**
 * 主题切换帮助类
 * 支持两种模式：
 * 1. 基于地理位置的日出日落时间自动切换
 * 2. 基于固定时间区间的降级切换方案
 */
public class ThemeHelper {

    /** 经度 */
    private static double mLongitude = 0;
    /** 纬度 */
    private static double mLatitude = 0;
    
    /** 默认早晨时间 - 6:00 */
    private static final int DEFAULT_MORNING_HOUR = 6;
    /** 默认晚上时间 - 18:00 */
    private static final int DEFAULT_EVENING_HOUR = 18;

    /**
     * 手动触发主题检测
     * 如果有位置信息，使用日出日落时间判断
     * 如果无位置信息，使用默认时间区间判断
     */
    public static void manualDetection() {
        if (mLongitude == 0 || mLatitude == 0) {
            MyLog.v("自动切换模式", "位置信息不可用，使用默认时间区间判断");
            updateThemeByTime();
            return;
        }
        updateThemeIfNeeded(mLongitude, mLatitude);
    }

    /**
     * 使用默认时间区间判断并切换主题
     * 早上6点到晚上18点为白天主题
     * 其他时间为夜间主题
     */
    private static void updateThemeByTime() {
        Calendar calendar = Calendar.getInstance();
        int currentHour = calendar.get(Calendar.HOUR_OF_DAY);
        
        // 判断当前是否是夜间模式
        boolean shouldBeNightMode = currentHour < DEFAULT_MORNING_HOUR || currentHour >= DEFAULT_EVENING_HOUR;
        
        if (shouldBeNightMode) {
            if (!SkinManager.getInstance().isNightModeNow()) {
                setNightSkin();
                MyLog.v("自动切换模式", "基于时间切换到夜间主题");
            }
        } else {
            if (SkinManager.getInstance().isNightModeNow()) {
                setDaySkin();
                MyLog.v("自动切换模式", "基于时间切换到白天主题");
            }
        }
    }

    /**
     * 根据经纬度更新主题
     * 通过计算日出日落时间来确定是否需要切换主题
     *
     * @param longitude 经度
     * @param latitude  纬度
     */
    public static void updateThemeIfNeeded(double longitude, double latitude) {
        //判断经纬度是否为空
        if (longitude == 0 || latitude == 0) {
            MyLog.v("自动切换模式             ", "经纬度为空 无法检测");
            return;
        }
        // 经度
        mLongitude = longitude;
        mLatitude = latitude;
        MyLog.v("自动切换模式             ", "正在检测主题信息   " + SkinManager.getInstance().isNightModeNow());
        // 判断用户是否开启了自动切换主题
        if (SettingsManager.getThemeMode() == 0) {
            // 获取日出日落时间
            String richuTime = SunRiseSet.getSunrise(new BigDecimal(longitude), new BigDecimal(latitude), new Date());
            String riluoTime = SunRiseSet.getSunset(new BigDecimal(longitude), new BigDecimal(latitude), new Date());
            
            // 转换为时间戳进行比较
            long currentTime = System.currentTimeMillis();
            long richuTimeMillis = TimeTools.getStringToDate(richuTime);
            long riluoTimeMillis = TimeTools.getStringToDate(riluoTime);
            
            // 根据时间段切换主题
            if (currentTime < richuTimeMillis) {
                // 日出前，使用夜间主题
                if (!SkinManager.getInstance().isNightModeNow()) {
                    setNightSkin();
                    MyLog.v("自动切换模式", "日出前，切换到夜间主题");
                }
            } else if (currentTime < riluoTimeMillis) {
                // 日出后、日落前，使用白天主题
                if (SkinManager.getInstance().isNightModeNow()) {
                    setDaySkin();
                    MyLog.v("自动切换模式", "白天时间，切换到日间主题");
                }
            } else {
                // 日落后，使用夜间主题
                if (!SkinManager.getInstance().isNightModeNow()) {
                    setNightSkin();
                    MyLog.v("自动切换模式", "日落后，切换到夜间主题");
                }
            }
        }
    }

    /**
     * 设置白天主题
     * 如果有自定义主题则使用自定义主题，否则使用默认主题
     */
    public static void setDaySkin() {
        // 判断是否有自定义主题，如果没有则恢复默认主题，否则加载自定义主题
        String dayTheme = SettingsManager.getDayTheme();
        if (dayTheme.isEmpty()) {
            SkinManager.getInstance().restoreDefaultTheme();
        } else {
            SkinManager.getInstance().loadSkin(dayTheme);
        }
    }

    /**
     * 设置夜间主题
     * 加载配置的夜间主题
     */
    public static void setNightSkin() {
        // 加载夜间主题
        SkinManager.getInstance().loadSkin(SettingsManager.getNightTheme());
    }
}
