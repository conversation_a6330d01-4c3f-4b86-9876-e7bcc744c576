package com.smartcar.easylauncher.core.manager;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;

import androidx.annotation.NonNull;


/**
 * Activity 管理器，用于获取顶部 Activity 的上下文
 */
public class ActivityManager {
    private static ActivityManager instance;
    private Activity topActivity;

    private ActivityManager(Application application) {
        registerActivityLifecycleCallbacks(application);
    }

    public static void init(Application application) {
        if (instance == null) {
            synchronized (ActivityManager.class) {
                if (instance == null) {
                    instance = new ActivityManager(application);
                }
            }
        }
    }

    private void registerActivityLifecycleCallbacks(Application application) {
        application.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(@NonNull Activity activity, Bundle savedInstanceState) {
            }

            @Override
            public void onActivityStarted(@NonNull Activity activity) {
            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {
                topActivity = activity;
            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {
            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {
            }

            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
            }

            @Override
            public void onActivityDestroyed(@NonNull Activity activity) {
                if (topActivity == activity) {
                    topActivity = null;
                }
            }
        });
    }

    public static ActivityManager getInstance() {
        return instance;
    }

    public Context getTopActivityContext() {
        return topActivity;
    }
}