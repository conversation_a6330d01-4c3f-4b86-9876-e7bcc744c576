package com.smartcar.easylauncher.core.manager;


import android.app.Application;

import com.smartcar.easylauncher.BuildConfig;
import com.smartcar.easylauncher.core.constants.AuthorityConstants;
import com.smartcar.easylauncher.shared.utils.encipher.SignUtil;
import com.smartcar.easylauncher.shared.utils.interceptor.TokenInterceptor;

import java.io.File;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import rxhttp.RxHttpPlugins;
import rxhttp.wrapper.cache.CacheMode;
import rxhttp.wrapper.cookie.CookieStore;
import rxhttp.wrapper.param.Method;
import rxhttp.wrapper.ssl.HttpsUtils;
import rxhttp.wrapper.ssl.HttpsUtils.SSLParams;


/**
 * 本类所有配置都是非必须的，根据自己需求选择就好
 * User:
 * Date:
 * Time:
 */
public class RxHttpManager {

    public static void init(Application context) {
        // 获取应用的外部缓存目录，并创建一个名为 RxHttpCookie 的文件，用于存储 Cookie
        File file = new File(context.getExternalCacheDir(), "RxHttpCookie");

        // 使用 HttpsUtils 获取 SSLSocketFactory
        SSLParams sslParams = HttpsUtils.getSslSocketFactory();

        // 创建 OkHttpClient.Builder，设置超时时间和 Cookie 持久化，以及信任所有证书，忽略 Hostname 验证，并添加了拦截器
        OkHttpClient client = new OkHttpClient.Builder()

                // 设置 Cookie 存储
                .cookieJar(new CookieStore(file))

                // 设置连接超时时间
                .connectTimeout(40, TimeUnit.SECONDS)

                // 设置读超时时间
                .readTimeout(40, TimeUnit.SECONDS)

                // 设置写超时时间
                .writeTimeout(40, TimeUnit.SECONDS)

                // 添加信任证书
                .sslSocketFactory(sslParams.sSLSocketFactory, sslParams.trustManager)

                // 忽略 host 验证
                .hostnameVerifier((hostname, session) -> true)

                // 默认是 true，如果不想自动处理重定向，可以禁止，然后用自定义的拦截器来实现
                //.followRedirects(false)

                // 添加自定义的拦截器
              //  .addInterceptor(new RedirectInterceptor())

                // 添加 TokenInterceptor，用于处理 token
                .addInterceptor(new TokenInterceptor())

                // 构建 OkHttpClient
                .build();

        // 设置缓存策略，创建一个名为 RxHttpCache 的文件用于缓存数据。这个不是必需的，根据需要设置
        File cacheFile = new File(context.getExternalCacheDir(), "RxHttpCache");

        // 通过 RxHttpPlugins 初始化，设置一些可选配置，如调试模式、缓存等，这个也是非必需的，根据需要调用
        RxHttpPlugins.init(client)
                // 设置是否开启调试模式以及日志的输出方式
                .setDebug(BuildConfig.DEBUG, false, 2)
                // 设置缓存文件和缓存过期时间，以及缓存模式
                .setCache(cacheFile, 1000 * 100, CacheMode.REQUEST_NETWORK_FAILED_READ_CACHE)
                // 设置某些 key 不参与 cacheKey 的组拼，可以在这里指定
                //.setExcludeCacheKeys("time")
                // 设置结果解码器，用于处理返回的数据
                //.setResultDecoder(s -> s)
                // 设置全局的数据转换器，默认可以不设置
                //.setConverter(FastJsonConverter.create())
                // 设置公共参数，可以根据不同的请求添加不同的参数
                .setOnParamAssembly(p -> {
                    // 获取当前请求的 Method 对象
                    Method method = p.getMethod();
                    // 根据请求方法添加不同的参数
//                    if (method.isGet()) {
//                        p.add("method", "get");
//                    } else if (method.isPost()) { //Post 请求
//                        p.add("method", "post");
//                    }
                    // 根据请求的 URL 添加不同的请求头
                    if (p.getUrl().contains("zhicheapi")) {
                        p.addHeader("X-LC-Id: " + AuthorityConstants.LC_ONE)
                                .addHeader("X-LC-Sign: " + SignUtil.generateSign(AuthorityConstants.LC_TWO))
                                .addHeader("Content-Type: application/json");
                    } else if (p.getUrl().contains("openweathermap")) {

                    } else {
                        // 添加一个公共的请求头
                        p.addHeader("Authorization", AuthorityManager.getToken());
                    }
                });
    }
}
