package com.smartcar.easylauncher.core.manager;


import com.smartcar.easylauncher.infrastructure.event.cody.NetworkStateScopeBus;
import com.smartcar.easylauncher.infrastructure.event.cody.SkinScopeBus;
import com.smartcar.easylauncher.infrastructure.interfaces.NetworkStatusListener;
import com.smartcar.easylauncher.infrastructure.interfaces.SkinChangeListener;
import com.smartcar.easylauncher.data.model.theme.SkinModel;
import com.smartcar.easylauncher.data.network.state.NetworkAvailabilityState;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import cody.bus.ObserverWrapper;

/**
 * <AUTHOR>
 */
public class ListenerManager {
    private static final String TAG = "ListenerManager";
    private static ListenerManager instance;
    private static final List<WeakReference<NetworkStatusListener>> networkStatusListeners = Collections.synchronizedList(new ArrayList<>());
    private static final List<WeakReference<SkinChangeListener>> skinChangeListeners = Collections.synchronizedList(new ArrayList<>());

    private ListenerManager() {
        initializeNetworkNotification(); // 初始化网络状态通知
        initializeSkinNotification(); // 初始化皮肤变化通知
    }

    /**
     * 获取单例实例
     * @return ListenerManager 实例
     */
    public static synchronized ListenerManager getInstance() {
        if (instance == null) {
            instance = new ListenerManager();
        }
        return instance;
    }

    /**
     * 初始化网络状态通知
     */
    private void initializeNetworkNotification() {
        MyLog.d(TAG, "初始化网络状态通知");
        NetworkStateScopeBus.networkAvailabilityState().observeForever(new ObserverWrapper<NetworkAvailabilityState>(true) {
            @Override
            public void onChanged(final NetworkAvailabilityState value) {
                MyLog.v(TAG, "收到网络状态变化事件");
                notifyNetworkStatus(value);
            }
        });
    }

    /**
     * 初始化皮肤变化通知
     */
    private void initializeSkinNotification() {
        MyLog.d(TAG, "初始化皮肤变化通知");
        SkinScopeBus.eventBean().observeForever(new ObserverWrapper<SkinModel>() {
            @Override
            public void onChanged(final SkinModel value) {
                MyLog.v(TAG, "收到皮肤变化事件");
                notifySkinChange(value);
            }
        });
    }

    /**
     * 通知所有注册的网络状态监听器
     * @param value 网络状态模型
     */
    private void notifyNetworkStatus(NetworkAvailabilityState value) {
        MyLog.d(TAG, "开始通知网络状态变化，当前监听器数量: " + networkStatusListeners.size());
        List<WeakReference<NetworkStatusListener>> invalidRefs = new ArrayList<>();
        for (WeakReference<NetworkStatusListener> ref : networkStatusListeners) {
            NetworkStatusListener listener = ref.get();
            if (listener != null) {
                try {
                    listener.onNetworkStatus(value);
                    MyLog.v(TAG, "成功通知网络状态变化到监听器: " + listener.getClass().getSimpleName());
                } catch (Exception e) {
                    MyLog.e(TAG, "通知网络状态变化异常: " + e.getMessage(), e);
                }
            } else {
                invalidRefs.add(ref);
                MyLog.d(TAG, "移除失效的网络状态监听器");
            }
        }
        networkStatusListeners.removeAll(invalidRefs);
        if (!invalidRefs.isEmpty()) {
            MyLog.d(TAG, "清理了 " + invalidRefs.size() + " 个失效的网络状态监听器");
        }
    }

    /**
     * 通知所有注册的皮肤变化监听器
     * @param value 皮肤模型
     */
    private void notifySkinChange(SkinModel value) {
        MyLog.d(TAG, "开始通知皮肤变化，当前监听器数量: " + skinChangeListeners.size());
        List<WeakReference<SkinChangeListener>> invalidRefs = new ArrayList<>();
        for (WeakReference<SkinChangeListener> ref : skinChangeListeners) {
            SkinChangeListener listener = ref.get();
            if (listener != null) {
                try {
                    listener.onSkinChange(value);
                    MyLog.v(TAG, "成功通知皮肤变化到监听器: " + listener.getClass().getSimpleName());
                } catch (Exception e) {
                    MyLog.e(TAG, "通知皮肤变化异常: " + e.getMessage(), e);
                }
            } else {
                invalidRefs.add(ref);
                MyLog.d(TAG, "移除失效的皮肤变化监听器");
            }
        }
        skinChangeListeners.removeAll(invalidRefs);
        if (!invalidRefs.isEmpty()) {
            MyLog.d(TAG, "清理了 " + invalidRefs.size() + " 个失效的皮肤变化监听器");
        }
    }

    /**
     * 注册网络状态监听器
     * @param listener 网络状态监听器
     */
    public void registerNetworkStatusListener(NetworkStatusListener listener) {
        MyLog.d(TAG, "注册网络状态监听器: " + listener.getClass().getSimpleName());
        networkStatusListeners.add(new WeakReference<>(listener));
    }

    /**
     * 注册皮肤变化监听器
     * @param listener 皮肤变化监听器
     */
    public void registerSkinChangeListener(SkinChangeListener listener) {
        MyLog.d(TAG, "注册皮肤变化监听器: " + listener.getClass().getSimpleName());
        skinChangeListeners.add(new WeakReference<>(listener));
    }

    /**
     * 注销网络状态监听器
     * @param listener 网络状态监听器
     */
    public void unregisterNetworkStatusListener(NetworkStatusListener listener) {
        MyLog.d(TAG, "注销网络状态监听器: " + listener.getClass().getSimpleName());
        List<WeakReference<NetworkStatusListener>> toRemove = new ArrayList<>();
        for (WeakReference<NetworkStatusListener> ref : networkStatusListeners) {
            NetworkStatusListener registeredListener = ref.get();
            if (registeredListener == null || registeredListener == listener) {
                toRemove.add(ref);
            }
        }
        networkStatusListeners.removeAll(toRemove);
    }

    /**
     * 注销皮肤变化监听器
     * @param listener 皮肤变化监听器
     */
    public void unregisterSkinChangeListener(SkinChangeListener listener) {
        MyLog.d(TAG, "注销皮肤变化监听器: " + listener.getClass().getSimpleName());
        List<WeakReference<SkinChangeListener>> toRemove = new ArrayList<>();
        for (WeakReference<SkinChangeListener> ref : skinChangeListeners) {
            SkinChangeListener registeredListener = ref.get();
            if (registeredListener == null || registeredListener == listener) {
                toRemove.add(ref);
            }
        }
        skinChangeListeners.removeAll(toRemove);
    }
}