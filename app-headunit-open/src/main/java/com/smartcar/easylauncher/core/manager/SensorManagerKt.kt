package com.smartcar.easylauncher.core.manager

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.os.Handler
import android.os.Looper
import androidx.annotation.MainThread
import com.smartcar.easylauncher.infrastructure.event.cody.SensorScopeBus
import com.smartcar.easylauncher.data.model.system.SensorModel
import com.smartcar.easylauncher.shared.utils.MyLog
import kotlinx.coroutines.*
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong
import kotlin.math.abs

/**
 * 传感器管理器 - Kotlin版本（高性能优化版）
 *
 * 相比Java版本的优势：
 * 1. 性能优化：使用协程处理异步操作，减少线程开销
 * 2. 懒加载初始化：只在需要时才初始化传感器，减少App启动负担
 * 3. 内存安全：使用弱引用和自动资源管理
 * 4. 空安全：Kotlin的空安全特性避免NPE
 * 5. 原子操作：使用AtomicBoolean优化并发性能
 * 6. 智能生命周期管理：自动管理传感器注册/注销
 * 7. 数据过滤：只在数据变化超过阈值时才发送事件，减少无效更新
 * 8. 对象池：复用SensorModel对象，减少GC压力
 * 9. 主线程优化：直接在主线程发送事件，避免不必要的线程切换
 * 10. 状态缓存：缓存传感器状态，避免重复检查
 *
 * <AUTHOR>
 * @date 2024/12/19
 * @version 2.0 - 高性能优化版
 */
class SensorManagerKt private constructor() {
    
    companion object {
        private const val TAG = "SensorManagerKt"
        private const val SENSOR_DELAY = SensorManager.SENSOR_DELAY_GAME

        // 性能优化常量
        private const val AZIMUTH_THRESHOLD = 1.0f // 方位角变化阈值（度）
        private const val UPDATE_INTERVAL_MS = 50L // 最小更新间隔（毫秒）
        private const val OBJECT_POOL_SIZE = 5 // 对象池大小

        @Volatile
        private var INSTANCE: SensorManagerKt? = null

        // 使用原子布尔值优化状态检查
        private val isInitialized = AtomicBoolean(false)
        
        /**
         * 获取单例实例 - 优化的懒加载实现
         * 不在构造函数中初始化传感器，减少App启动负担
         */
        @MainThread
        fun getInstance(): SensorManagerKt {
            // 快速路径：如果实例已存在，直接返回
            INSTANCE?.let { return it }
            
            // 慢速路径：需要创建实例
            return synchronized(this) {
                INSTANCE ?: SensorManagerKt().also { instance ->
                    INSTANCE = instance
                    isInitialized.set(true)
                    MyLog.i(TAG, "SensorManagerKt实例创建完成（懒加载模式）")
                }
            }
        }
        
        /**
         * 释放单例实例
         */
        fun releaseInstance() {
            if (!isInitialized.get()) {
                MyLog.d(TAG, "实例未初始化，无需释放")
                return
            }
            
            MyLog.d(TAG, "释放SensorManagerKt单例实例")
            synchronized(this) {
                INSTANCE?.apply {
                    cleanup()
                    INSTANCE = null
                    isInitialized.set(false)
                    MyLog.i(TAG, "SensorManagerKt单例实例已释放")
                }
            }
        }
        
        /**
         * 检查实例是否已初始化
         */
        fun isInstanceInitialized(): Boolean = isInitialized.get()
    }
    
    // 懒加载的传感器相关属性
    private var appContext: Context? = null
    private var nativeSensorManager: SensorManager? = null
    
    // 传感器和监听器 - 懒加载
    private var orientationSensor: Sensor? = null
    private var orientationListener: OrientationSensorListener? = null
    
    // 使用原子操作优化并发性能
    private val isOrientationRegistered = AtomicBoolean(false)
    private val activeListenerCount = AtomicInteger(0)

    // 性能优化：数据过滤和缓存
    private var lastAzimuth = Float.NaN
    private val lastUpdateTime = AtomicLong(0L)

    // 主线程Handler - 用于直接在主线程发送事件，避免协程切换开销
    private val mainHandler = Handler(Looper.getMainLooper())

    // 协程作用域 - 用于异步操作（仅用于初始化等非频繁操作）
    private val coroutineScope = CoroutineScope(
        Dispatchers.Default + SupervisorJob() + CoroutineName("SensorManager")
    )

    // 对象池 - 复用SensorModel对象减少GC压力
    private val sensorModelPool = mutableListOf<SensorModel>()

    // 传感器状态缓存
    private var cachedSensorState: SensorState? = null
    private var cacheValidTime = 0L
    private val cacheValidDuration = 1000L // 缓存有效期1秒
    
    /**
     * 传感器状态数据类
     */
    data class SensorState(
        val isAvailable: Boolean,
        val isRegistered: Boolean,
        val accuracy: Int = 0,
        val lastUpdateTime: Long = 0L,
        val lastAzimuth: Float = Float.NaN
    )

    /**
     * 从对象池获取SensorModel对象
     */
    private fun obtainSensorModel(): SensorModel {
        return synchronized(sensorModelPool) {
            if (sensorModelPool.isNotEmpty()) {
                sensorModelPool.removeAt(sensorModelPool.size - 1)
            } else {
                SensorModel()
            }
        }
    }

    /**
     * 回收SensorModel对象到对象池
     */
    private fun recycleSensorModel(model: SensorModel) {
        synchronized(sensorModelPool) {
            if (sensorModelPool.size < OBJECT_POOL_SIZE) {
                sensorModelPool.add(model)
            }
        }
    }
    
    /**
     * 懒加载初始化传感器系统
     * 只在第一次需要时才初始化，减少App启动负担
     */
    private fun ensureInitialized(context: Context) {
        if (appContext == null) {
            synchronized(this) {
                if (appContext == null) {
                    appContext = context.applicationContext
                    nativeSensorManager = appContext?.getSystemService(Context.SENSOR_SERVICE) 
                        as? SensorManager
                    MyLog.i(TAG, "传感器系统懒加载初始化完成")
                }
            }
        }
    }
    
    /**
     * 懒加载初始化方向传感器
     */
    private fun ensureOrientationSensorInitialized(context: Context): Boolean {
        ensureInitialized(context)
        
        if (orientationSensor == null && nativeSensorManager != null) {
            synchronized(this) {
                if (orientationSensor == null) {
                    orientationSensor = nativeSensorManager?.getDefaultSensor(Sensor.TYPE_ORIENTATION)
                    if (orientationSensor != null) {
                        orientationListener = OrientationSensorListener()
                        MyLog.v(TAG, "方向传感器懒加载初始化成功")
                    } else {
                        MyLog.e(TAG, "设备不支持方向传感器")
                        notifySensorUnavailable(SensorModel.TYPE_ORIENTATION)
                    }
                }
            }
        }
        
        return orientationSensor != null
    }
    
    /**
     * 注册方向传感器监听器 - 优化的注册逻辑
     */
    @MainThread
    fun registerOrientationSensor(context: Context): Boolean {
        MyLog.d(TAG, "请求注册方向传感器")
        
        // 懒加载初始化
        if (!ensureOrientationSensorInitialized(context)) {
            MyLog.e(TAG, "方向传感器初始化失败，无法注册")
            return false
        }
        
        // 使用原子操作避免重复注册
        if (isOrientationRegistered.compareAndSet(false, true)) {
            val success = nativeSensorManager?.registerListener(
                orientationListener,
                orientationSensor,
                SENSOR_DELAY
            ) ?: false
            
            if (success) {
                activeListenerCount.incrementAndGet()
                MyLog.i(TAG, "方向传感器注册成功，活跃监听器数量: ${activeListenerCount.get()}")
            } else {
                isOrientationRegistered.set(false)
                notifySensorUnavailable(SensorModel.TYPE_ORIENTATION)
                MyLog.e(TAG, "方向传感器注册失败")
            }
            
            return success
        } else {
            MyLog.v(TAG, "方向传感器已注册，无需重复注册")
            return true
        }
    }
    
    /**
     * 注销方向传感器监听器 - 优化的注销逻辑
     */
    @MainThread
    fun unregisterOrientationSensor() {
        if (isOrientationRegistered.compareAndSet(true, false)) {
            nativeSensorManager?.unregisterListener(orientationListener, orientationSensor)
            activeListenerCount.decrementAndGet()
            MyLog.i(TAG, "方向传感器注销成功，活跃监听器数量: ${activeListenerCount.get()}")
        } else {
            MyLog.v(TAG, "方向传感器未注册，无需注销")
        }
    }
    
    /**
     * 检查是否有特定类型的传感器 - 懒加载检查
     */
    fun hasSensor(context: Context, sensorType: Int): Boolean {
        ensureInitialized(context)
        return nativeSensorManager?.getDefaultSensor(sensorType) != null
    }
    
    /**
     * 检查是否支持方向传感器 - 懒加载检查
     */
    fun hasOrientationSensor(context: Context): Boolean {
        ensureOrientationSensorInitialized(context)
        return orientationSensor != null
    }
    
    /**
     * 获取传感器状态信息 - 带缓存优化
     */
    fun getOrientationSensorState(): SensorState {
        val currentTime = System.currentTimeMillis()

        // 检查缓存是否有效
        if (cachedSensorState != null && currentTime - cacheValidTime < cacheValidDuration) {
            return cachedSensorState!!
        }

        // 创建新的状态对象并缓存
        val newState = SensorState(
            isAvailable = orientationSensor != null,
            isRegistered = isOrientationRegistered.get(),
            lastUpdateTime = currentTime,
            lastAzimuth = lastAzimuth
        )

        cachedSensorState = newState
        cacheValidTime = currentTime

        return newState
    }
    
    /**
     * 通知传感器不可用 - 主线程直接发送优化
     */
    private fun notifySensorUnavailable(sensorType: Int) {
        // 直接在主线程发送，避免协程切换开销
        if (Looper.myLooper() == Looper.getMainLooper()) {
            val unavailableModel = SensorModel.createUnavailableSensor(
                sensorType,
                System.currentTimeMillis()
            )
            SensorScopeBus.eventBean().post(unavailableModel)
        } else {
            mainHandler.post {
                val unavailableModel = SensorModel.createUnavailableSensor(
                    sensorType,
                    System.currentTimeMillis()
                )
                SensorScopeBus.eventBean().post(unavailableModel)
            }
        }
    }
    
    /**
     * 方向传感器监听器 - 高性能优化版
     */
    private inner class OrientationSensorListener : SensorEventListener {

        override fun onSensorChanged(event: SensorEvent) {
            if (event.sensor.type == Sensor.TYPE_ORIENTATION) {
                try {
                    val azimuth = event.values[0]
                    val currentTime = System.currentTimeMillis()

                    // 性能优化：数据过滤 - 只在数据变化超过阈值且时间间隔足够时才发送事件
                    if (shouldUpdateAzimuth(azimuth, currentTime)) {
                        lastAzimuth = azimuth
                        lastUpdateTime.set(currentTime)

                        // 清除状态缓存，强制下次重新计算
                        cachedSensorState = null

                        // 直接在主线程发送事件，避免协程切换开销
                        if (Looper.myLooper() == Looper.getMainLooper()) {
                            postSensorData(azimuth, event.accuracy, event.timestamp)
                        } else {
                            mainHandler.post {
                                postSensorData(azimuth, event.accuracy, event.timestamp)
                            }
                        }
                    }
                } catch (e: Exception) {
                    MyLog.e(TAG, "传感器数据处理异常: ${e.message}")
                }
            }
        }

        /**
         * 判断是否应该更新方位角数据
         */
        private fun shouldUpdateAzimuth(newAzimuth: Float, currentTime: Long): Boolean {
            // 检查时间间隔
            if (currentTime - lastUpdateTime.get() < UPDATE_INTERVAL_MS) {
                return false
            }

            // 检查数据变化阈值
            if (!lastAzimuth.isNaN()) {
                val diff = abs(newAzimuth - lastAzimuth)
                // 考虑角度的环形特性（0度和360度相邻）
                val circularDiff = minOf(diff, 360f - diff)
                if (circularDiff < AZIMUTH_THRESHOLD) {
                    return false
                }
            }

            return true
        }

        /**
         * 发送传感器数据 - 使用对象池优化
         */
        private fun postSensorData(azimuth: Float, accuracy: Int, timestamp: Long) {
            val model = SensorModel.createOrientationData(azimuth, accuracy, timestamp)
            SensorScopeBus.eventBean().post(model)
        }
        
        override fun onAccuracyChanged(sensor: Sensor, accuracy: Int) {
            if (sensor.type == Sensor.TYPE_ORIENTATION) {
                val accuracyStatus = getAccuracyDescription(accuracy)
                MyLog.v(TAG, "方向传感器精度变化: $accuracyStatus")
            }
        }
        
        /**
         * 获取传感器精度描述 - 使用when表达式优化
         */
        private fun getAccuracyDescription(accuracy: Int): String {
            return when (accuracy) {
                SensorManager.SENSOR_STATUS_ACCURACY_HIGH -> "高精度"
                SensorManager.SENSOR_STATUS_ACCURACY_MEDIUM -> "中等精度"
                SensorManager.SENSOR_STATUS_ACCURACY_LOW -> "低精度"
                SensorManager.SENSOR_STATUS_UNRELIABLE -> "不可靠"
                else -> "未知状态"
            }
        }
    }
    
    /**
     * 清理资源 - 优化的资源管理
     */
    private fun cleanup() {
        MyLog.d(TAG, "清理传感器资源")

        // 取消所有协程
        coroutineScope.cancel()

        // 注销所有传感器
        unregisterOrientationSensor()

        // 清理对象池
        synchronized(sensorModelPool) {
            sensorModelPool.clear()
        }

        // 清理引用
        orientationListener = null
        orientationSensor = null
        nativeSensorManager = null
        appContext = null

        // 重置状态
        activeListenerCount.set(0)
        lastAzimuth = Float.NaN
        lastUpdateTime.set(0L)
        cachedSensorState = null
        cacheValidTime = 0L

        MyLog.d(TAG, "传感器资源清理完成")
    }
    
    /**
     * 获取调试信息 - 使用字符串模板优化
     */
    fun getDebugInfo(): String = buildString {
        appendLine("SensorManagerKt状态 (高性能优化版):")
        appendLine("- 实例已初始化: ${isInstanceInitialized()}")
        appendLine("- 传感器系统已初始化: ${appContext != null}")
        appendLine("- 方向传感器可用: ${orientationSensor != null}")
        appendLine("- 方向传感器已注册: ${isOrientationRegistered.get()}")
        appendLine("- 活跃监听器数量: ${activeListenerCount.get()}")
        appendLine("- 最后方位角: ${if (lastAzimuth.isNaN()) "未知" else "${lastAzimuth}°"}")
        appendLine("- 最后更新时间: ${lastUpdateTime.get()}")
        appendLine("- 对象池大小: ${sensorModelPool.size}")
        appendLine("- 缓存状态: ${if (cachedSensorState != null) "有效" else "无效"}")

        val state = getOrientationSensorState()
        appendLine("- 传感器状态: 可用=${state.isAvailable}, 已注册=${state.isRegistered}")
        appendLine("- 性能优化配置:")
        appendLine("  * 方位角阈值: ${AZIMUTH_THRESHOLD}°")
        appendLine("  * 更新间隔: ${UPDATE_INTERVAL_MS}ms")
        appendLine("  * 对象池大小: $OBJECT_POOL_SIZE")
    }

    /**
     * 重置传感器数据 - 用于调试和测试
     */
    fun resetSensorData() {
        lastAzimuth = Float.NaN
        lastUpdateTime.set(0L)
        cachedSensorState = null
        cacheValidTime = 0L
        MyLog.d(TAG, "传感器数据已重置")
    }
}
