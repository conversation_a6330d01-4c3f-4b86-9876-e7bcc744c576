package com.smartcar.easylauncher.core.manager

import android.content.Context
import android.content.Intent
import android.util.TypedValue
import android.view.View
import android.view.ViewTreeObserver
import androidx.annotation.MainThread
import com.google.gson.Gson
import com.hjq.shape.drawable.ShapeDrawable
import com.maning.mndialoglibrary.MToast
import com.smartcar.easylauncher.R
import com.smartcar.easylauncher.core.constants.SettingsConstants
import com.smartcar.easylauncher.data.model.system.AppInfo
import com.smartcar.easylauncher.infrastructure.skin.SkinManager
import com.smartcar.easylauncher.shared.utils.MyLog
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils
import kotlinx.coroutines.*
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.Collections

/**
 * Scene模式专用地图管理器 - Kotlin版本
 *
 * 相比Java版本的优势：
 * 1. 性能优化：使用协程处理异步操作，减少线程开销
 * 2. 内存安全：使用弱引用和自动资源管理
 * 3. 空安全：Kotlin的空安全特性避免NPE
 * 4. 简洁语法：减少样板代码，提高可读性
 * 5. 函数式编程：使用高阶函数和lambda表达式
 * 6. 智能类型推断：减少显式类型声明
 * 7. 弹窗状态管理：集成DialogStateManager，支持百度地图弹窗适配
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
class SceneMapManagerKt private constructor(context: Context) : DialogStateManager.DialogStateChangeListener {

    companion object {
        private const val TAG = "SceneMapManagerKt"
        private const val DELAY_CHECK_TIME = 100L // 延迟检查时间
        private const val CORNER_RADIUS_DP = 16f // 圆角半径
        private const val STROKE_SIZE_DP = 7f // 边框宽度
        private const val MARGIN_SIZE_DP = 5f // 边距大小

        // 循环检测相关常量 - 优化后的参数
        private const val OPERATION_HISTORY_SIZE = 15 // 记录最近15次操作
        private const val LOOP_DETECTION_WINDOW = 3000L // 3秒检测窗口（缩短窗口，提高精度）
        private const val LOOP_THRESHOLD = 8 // 3秒内超过8次操作才视为循环（提高阈值）
        private const val DISABLE_DURATION = 15000L // 禁用15秒（缩短禁用时间）

        @Volatile
        private var INSTANCE: SceneMapManagerKt? = null

        // 使用原子布尔值优化状态检查
        private val isInitialized = AtomicBoolean(false)

        /**
         * 获取单例实例 - 优化的线程安全实现
         */
        @MainThread
        fun getInstance(context: Context): SceneMapManagerKt {
            // 快速路径：如果实例已存在，直接返回
            INSTANCE?.let { return it }

            // 慢速路径：需要创建实例
            return synchronized(this) {
                INSTANCE ?: SceneMapManagerKt(context.applicationContext).also { instance ->
                    INSTANCE = instance
                    isInitialized.set(true)
                    MyLog.i(TAG, "SceneMapManagerKt实例创建完成")
                }
            }
        }

        /**
         * 释放单例实例 - 优化的清理逻辑
         */
        fun releaseInstance() {
            if (!isInitialized.get()) {
                MyLog.d(TAG, "实例未初始化，无需释放")
                return
            }

            MyLog.d(TAG, "释放SceneMapManagerKt单例实例")
            synchronized(this) {
                INSTANCE?.apply {
                    cleanup()
                    INSTANCE = null
                    isInitialized.set(false)
                    MyLog.i(TAG, "SceneMapManagerKt单例实例已释放")
                }
            }
        }

        /**
         * 检查实例是否已初始化
         */
        fun isInstanceInitialized(): Boolean = isInitialized.get()
    }

    // 使用应用上下文避免内存泄漏
    private val appContext: Context = context.applicationContext

    // Scene状态管理 - 使用线程安全的ConcurrentHashMap
    private val sceneStates = ConcurrentHashMap<String, SceneMapState>()

    // 当前状态 - 使用原子操作优化并发性能
    @Volatile
    private var currentActiveScene: String? = null

    // 使用原子布尔值优化频繁读取的状态
    private val isActivityFocused = AtomicBoolean(false)

    // 地图视图管理 - 使用弱引用避免内存泄漏
    private var currentMapView: WeakReference<View>? = null

    // 使用懒加载优化ShapeDrawable创建
    private val shapeDrawable: ShapeDrawable by lazy { ShapeDrawable() }

    // 协程作用域 - 用于异步操作，使用IO调度器优化性能
    private val coroutineScope = CoroutineScope(
        Dispatchers.Main.immediate + SupervisorJob() + CoroutineName("SceneMapManager")
    )

    // 延迟检查任务
    private var delayedCheckJob: Job? = null

    // 循环检测和保护机制 - 线程安全
    private val mapOperationHistory = Collections.synchronizedList(mutableListOf<MapOperation>())
    @Volatile
    private var isMapFunctionDisabled = false
    @Volatile
    private var disableStartTime = 0L

    /**
     * 地图操作记录
     */
    private data class MapOperation(
        val type: OperationType,
        val timestamp: Long,
        val reason: String
    )

    /**
     * 操作类型枚举
     */
    private enum class OperationType {
        SHOW, HIDE
    }

    /**
     * Scene地图状态数据类 - 优化的状态管理
     */
    data class SceneMapState(
        val isNavigationScene: Boolean,
        var mapContainer: View? = null,
        var isVisible: Boolean = false,
        var shouldShowMap: Boolean = false
    ) {
        /**
         * 检查是否满足地图显示的Scene条件
         */
        fun canShowMap(): Boolean {
            val container = mapContainer // 创建本地副本避免智能转换问题
            return isVisible &&
                   shouldShowMap &&
                   isNavigationScene &&
                   container != null &&
                   container.visibility == View.VISIBLE
        }

        /**
         * 获取状态摘要用于调试
         */
        fun getStatusSummary(): String {
            return "visible=$isVisible, shouldShow=$shouldShowMap, isNav=$isNavigationScene, hasContainer=${mapContainer != null}"
        }
    }

    init {
        MyLog.i(TAG, "SceneMapManagerKt初始化完成")

        // 注册弹窗状态监听器
        DialogStateManager.addStateChangeListener(this)
        MyLog.d(TAG, "已注册弹窗状态监听器")
    }

    /**
     * 注册Scene - 优化的参数验证和错误处理
     */
    fun registerScene(
        sceneId: String,
        isNavigationScene: Boolean,
        mapContainer: View? = null
    ): SceneMapManagerKt = apply {
        // 参数验证
        require(sceneId.isNotBlank()) { "Scene ID 不能为空" }

        MyLog.d(TAG, "注册Scene: $sceneId, 导航Scene: $isNavigationScene")

        // 检查是否重复注册
        if (sceneStates.containsKey(sceneId)) {
            MyLog.w(TAG, "Scene已存在，将覆盖: $sceneId")
        }

        val state = SceneMapState(
            isNavigationScene = isNavigationScene,
            mapContainer = mapContainer
        )
        sceneStates[sceneId] = state

        MyLog.i(TAG, "Scene注册成功: $sceneId")
    }

    /**
     * 注销Scene
     */
    fun unregisterScene(sceneId: String): SceneMapManagerKt = apply {
        MyLog.d(TAG, "注销Scene: $sceneId")

        sceneStates.remove(sceneId)?.let {
            // 如果当前活跃Scene被注销，清空并隐藏地图
            if (sceneId == currentActiveScene) {
                currentActiveScene = null
                hideMapInternal()
            }
            MyLog.i(TAG, "Scene注销成功: $sceneId")
        } ?: MyLog.w(TAG, "Scene未找到: $sceneId")
    }

    /**
     * 设置Scene的地图容器
     * 用于已注册的Scene后续设置地图容器
     */
    fun setSceneMapContainer(sceneId: String, mapContainer: View?): SceneMapManagerKt = apply {
        val state = sceneStates[sceneId]
        if (state != null) {
            state.mapContainer = mapContainer
            MyLog.d(TAG, "设置Scene地图容器: $sceneId")
        } else {
            MyLog.w(TAG, "尝试为未注册的Scene设置地图容器: $sceneId")
        }
    }

    /**
     * Scene可见性变化 - 使用协程优化性能，支持百度地图适配
     */
    fun onSceneVisibilityChanged(sceneId: String, visible: Boolean): SceneMapManagerKt = apply {
        MyLog.d(TAG, "Scene可见性变化: $sceneId -> $visible")

        sceneStates[sceneId]?.let { state ->
            state.isVisible = visible

            if (visible) {
                // Scene变为可见，设置为当前活跃Scene
                val previousScene = currentActiveScene
                currentActiveScene = sceneId
                MyLog.i(TAG, "活跃Scene变更: $previousScene -> $sceneId")

                // Scene切换时清理可能的残留弹窗
                if (previousScene != null && previousScene != sceneId) {
                    MyLog.d(TAG, "Scene切换，清理残留弹窗")
                    DialogStateManager.forceCleanupAutoDetectedDialogs()
                }

                // 根据导航软件类型选择检查策略
                if (isBaiduNavigation()) {
                    MyLog.d(TAG, "百度导航模式 - Scene可见时直接检查地图显示")
                    // 百度导航：直接检查，不依赖Activity焦点
                    checkAndUpdateMapDisplayForBaidu()
                } else {
                    // 标准导航：异步检查地图显示状态
                    scheduleMapCheck("Scene可见: $sceneId")
                }
            } else {
                // Scene变为不可见 - 百度导航模式优化
                if (sceneId == currentActiveScene) {
                    currentActiveScene = null
                    MyLog.i(TAG, "活跃Scene清空: $sceneId")

                    // 强制清理可能的残留弹窗
                    DialogStateManager.forceCleanupAutoDetectedDialogs()

                    // 百度导航模式：立即隐藏，避免延迟
                    if (isBaiduNavigation()) {
                        MyLog.d(TAG, "百度导航模式 - Scene不可见时立即隐藏地图")
                        hideMapInternal()
                    } else {
                        hideMapInternal()
                    }
                }
            }
        } ?: MyLog.w(TAG, "Scene状态未找到: $sceneId")
    }

    /**
     * Activity焦点变化 - 优化的原子操作，支持百度地图适配
     */
    fun onActivityFocusChanged(hasFocus: Boolean): SceneMapManagerKt = apply {
        // 使用原子操作避免不必要的处理
        if (isActivityFocused.compareAndSet(!hasFocus, hasFocus)) {
            MyLog.d(TAG, "Activity焦点变化: $hasFocus")

            // 检查是否为百度导航，采用不同的处理策略
            if (isBaiduNavigation()) {
                MyLog.d(TAG, "检测到百度导航，使用百度地图适配逻辑")
                handleBaiduNavigationFocusChange(hasFocus)
            } else {
                MyLog.d(TAG, "使用高德地图标准逻辑")
                handleStandardNavigationFocusChange(hasFocus)
            }
        } else {
            MyLog.v(TAG, "Activity焦点状态未变化: $hasFocus")
        }
    }

    /**
     * Activity进入后台 - 百度导航模式优化
     * 直接隐藏地图，避免依赖焦点变化的延迟
     */
    fun onActivityPause(): SceneMapManagerKt = apply {
        MyLog.d(TAG, "Activity进入后台")

        if (isBaiduNavigation()) {
            MyLog.d(TAG, "百度导航模式 - Activity后台时立即隐藏地图")
            hideMapInternal()
        } else {
            MyLog.d(TAG, "标准导航模式 - 使用正常的焦点处理逻辑")
            // 标准导航模式不需要特殊处理，依赖焦点变化
        }
    }

    /**
     * 预测性隐藏地图 - 用于Activity即将切换时提前隐藏
     * 百度导航模式专用，避免等待焦点变化的延迟
     */
    fun predictiveHideMap(reason: String = "预测性隐藏"): SceneMapManagerKt = apply {
        if (isBaiduNavigation()) {
            MyLog.d(TAG, "百度导航模式 - 预测性隐藏地图: $reason")
            hideMapInternal()
        } else {
            MyLog.v(TAG, "标准导航模式 - 跳过预测性隐藏: $reason")
        }
    }

    /**
     * 设置Scene是否应该显示地图 - 支持百度地图适配
     */
    fun setSceneShouldShowMap(sceneId: String, shouldShow: Boolean): SceneMapManagerKt = apply {
        MyLog.d(TAG, "设置Scene地图显示: $sceneId -> $shouldShow")

        sceneStates[sceneId]?.let { state ->
            state.shouldShowMap = shouldShow

            // 如果是当前活跃Scene，立即检查地图显示状态
            if (sceneId == currentActiveScene) {
                if (isBaiduNavigation()) {
                    MyLog.d(TAG, "百度导航模式 - 设置地图显示状态时直接检查")
                    // 百度导航：直接检查，不依赖Activity焦点
                    checkAndUpdateMapDisplayForBaidu()
                } else {
                    // 标准导航：使用延迟检查
                    scheduleMapCheck("设置地图显示状态")
                }
            }
        } ?: MyLog.w(TAG, "Scene状态未找到: $sceneId")
    }

    /**
     * 安排延迟的地图检查 - 使用协程替代Handler
     */
    private fun scheduleMapCheck(reason: String) {
        MyLog.d(TAG, "安排延迟地图检查: $reason")

        // 取消之前的检查任务
        cancelMapCheck()

        // 使用协程延迟执行
        delayedCheckJob = coroutineScope.launch {
            delay(DELAY_CHECK_TIME)
            MyLog.d(TAG, "执行延迟地图检查: $reason")
            checkAndUpdateMapDisplay()
        }
    }

    /**
     * 取消延迟的地图检查
     */
    private fun cancelMapCheck() {
        delayedCheckJob?.cancel()
        delayedCheckJob = null
    }

    /**
     * 检查并更新地图显示状态 - 优化的条件检查逻辑，支持弹窗状态检查
     */
    private fun checkAndUpdateMapDisplay() {
        MyLog.d(TAG, "检查地图显示状态")
        MyLog.v(TAG, "Activity焦点: ${isActivityFocused.get()}, 活跃Scene: $currentActiveScene")

        // 检查弹窗状态 - 如果有弹窗显示，立即隐藏地图
        if (DialogStateManager.hasActiveDialogs()) {
            MyLog.w(TAG, "有弹窗显示，隐藏地图 - 弹窗数量: ${DialogStateManager.getActiveDialogCount()}")
            hideMapInternal()
            return
        }

        // 快速失败检查 - 优化性能
        if (!isActivityFocused.get()) {
            MyLog.w(TAG, "Activity未获得焦点")
            hideMapInternal()
            return
        }

        val sceneId = currentActiveScene
        if (sceneId == null) {
            MyLog.w(TAG, "无活跃Scene")
            hideMapInternal()
            return
        }

        val state = sceneStates[sceneId]
        if (state == null) {
            MyLog.w(TAG, "Scene状态未找到: $sceneId")
            hideMapInternal()
            return
        }

        // 使用优化的条件检查方法
        if (!state.canShowMap()) {
            MyLog.w(TAG, "Scene条件不满足: ${state.getStatusSummary()}")
            hideMapInternal()
            return
        }

        // 检查系统设置
        if (!checkSystemSettings()) {
            MyLog.w(TAG, "系统设置不满足地图显示条件")
            hideMapInternal()
            return
        }

        // 所有条件满足，显示地图
        MyLog.i(TAG, "条件满足，显示地图: $sceneId")
        val container = state.mapContainer // 创建本地副本避免智能转换问题
        container?.let { showMapInternal(it) }
    }

    /**
     * 检查系统设置 - 提取为独立方法提高可读性
     */
    private fun checkSystemSettings(): Boolean {
        val defaultHome = SettingsManager.getDefaultHome()
        val naviShowMode = SettingsManager.getNaviShowMode()

        MyLog.v(TAG, "系统设置检查: defaultHome=$defaultHome, naviShowMode=$naviShowMode")

        return defaultHome == SettingsConstants.HOME_MAP_LAYOUT_TYPE &&
                naviShowMode == SettingsConstants.HOME_MAP_LAYOUT_TYPE
    }

    /**
     * 内部显示地图方法 - 使用扩展函数优化，支持循环检测
     */
    private fun showMapInternal(mapViewContainer: View) {
        // 检查地图功能是否被禁用
        if (isMapFunctionDisabled) {
            val currentTime = System.currentTimeMillis()
            if (currentTime - disableStartTime < DISABLE_DURATION) {
                MyLog.w(TAG, "地图功能已禁用，剩余时间: ${(DISABLE_DURATION - (currentTime - disableStartTime)) / 1000}秒")
                return
            } else {
                // 禁用时间到，重新启用
                enableMapFunction()
            }
        }

        // 记录显示操作
        recordMapOperation(OperationType.SHOW, "显示地图")

        // 检查是否存在循环
        if (detectLoop()) {
            disableMapFunction()
            return
        }

        MyLog.d(TAG, "显示地图")

        currentMapView = WeakReference(mapViewContainer)

        // 使用apply作用域函数简化代码
        val location = IntArray(2).apply {
            mapViewContainer.getLocationOnScreen(this)
        }

        val (leftTopX, leftTopY) = location
        val rightTopX = leftTopX + mapViewContainer.width
        val leftBottomY = leftTopY + mapViewContainer.height

        MyLog.v(
            TAG,
            "地图位置: ($leftTopX, $leftTopY), 尺寸: ${mapViewContainer.width}x${mapViewContainer.height}"
        )

        // 发送显示地图广播
        val intent = Intent("com.autonavi.plus.showmap").apply {
            putExtra("x", leftTopX - handleMargin("x"))
            putExtra("y", leftTopY - handleMargin("y"))
            putExtra("w", rightTopX + handleMargin("w"))
            putExtra("h", leftBottomY + handleMargin("h"))
        }

        appContext.sendBroadcast(intent)
        MyLog.i(TAG, "发送显示地图广播")

        // 处理边框显示
        if (SettingsManager.getFloatingMap() == 2) {
            showBorder(mapViewContainer)
        }
    }

    /**
     * 内部隐藏地图方法 - 支持循环检测
     */
    private fun hideMapInternal() {
        // 检查地图功能是否被禁用
        if (isMapFunctionDisabled) {
            val currentTime = System.currentTimeMillis()
            if (currentTime - disableStartTime < DISABLE_DURATION) {
                MyLog.w(TAG, "地图功能已禁用，跳过隐藏操作")
                return
            } else {
                // 禁用时间到，重新启用
                enableMapFunction()
            }
        }

        // 记录隐藏操作
        recordMapOperation(OperationType.HIDE, "隐藏地图")

        // 检查是否存在循环
        if (detectLoop()) {
            disableMapFunction()
            return
        }

        MyLog.d(TAG, "隐藏地图")

        // 发送隐藏地图广播
        val intent = Intent("com.autonavi.plus.closemap")
        appContext.sendBroadcast(intent)

        // 处理边框隐藏
        if (SettingsManager.getFloatingMap() == 2) {
            currentMapView?.get()?.let { hideBorder(it) }
        }

        currentMapView = null
    }

    /**
     * 销毁地图
     */
    fun destroyMap(): SceneMapManagerKt = apply {
        MyLog.d(TAG, "销毁地图")

        val intent = Intent("com.autonavi.plus.closemap")
        appContext.sendBroadcast(intent)

        currentMapView = null
    }

    /**
     * 处理边距参数 - 使用when表达式
     */
    private fun handleMargin(type: String): Int {
        val floatingMapMode = SettingsManager.getFloatingMap()

        return when (floatingMapMode) {
            0 -> 0
            1 -> mapLayoutMargin(type)
            2 -> DensityUtils.dp2px(appContext, -6f)
            else -> 0
        }
    }

    /**
     * 计算地图布局边距 - 使用when表达式优化
     */
    private fun mapLayoutMargin(type: String): Int {
        val mapLayout = SettingsManager.getMapLayout()

        return when (mapLayout) {
            SettingsConstants.DEFAULT_MAP_LAYOUT_1,
            SettingsConstants.DEFAULT_MAP_LAYOUT_2,
            SettingsConstants.DEFAULT_MAP_LAYOUT_5 -> DensityUtils.dp2px(appContext, MARGIN_SIZE_DP)

            SettingsConstants.DEFAULT_MAP_LAYOUT_3,
            SettingsConstants.DEFAULT_MAP_LAYOUT_4 -> {
                if (type == "h") 0 // 高度方向不需要边距
                else DensityUtils.dp2px(appContext, MARGIN_SIZE_DP)
            }

            else -> 0
        }
    }

    /**
     * 显示边框 - 优化的边框管理
     */
    private fun showBorder(mapView: View) {
        MyLog.d(TAG, "显示边框")

        val cornerRadius = DensityUtils.dp2px(appContext, CORNER_RADIUS_DP)
        val strokeSize = TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP, STROKE_SIZE_DP,
            appContext.resources.displayMetrics
        ).toInt()

        shapeDrawable.apply {
            setRadius(cornerRadius.toFloat())
            setStrokeSize(strokeSize)
            setStrokeColor(SkinManager.getInstance().getColor(R.color.map_default))
            intoBackground(mapView)
        }

        MyLog.v(TAG, "边框显示完成: 宽度=${strokeSize}px, 圆角=${cornerRadius}px")
    }

    /**
     * 隐藏边框 - 优化的边框管理
     */
    private fun hideBorder(mapView: View) {
        MyLog.d(TAG, "隐藏边框")

        val cornerRadius = DensityUtils.dp2px(appContext, CORNER_RADIUS_DP)

        shapeDrawable.apply {
            setRadius(cornerRadius.toFloat())
            setStrokeSize(0)
            setStrokeColor(SkinManager.getInstance().getColor(R.color.map_default))
            intoBackground(mapView)
        }

        MyLog.v(TAG, "边框隐藏完成，保持圆角: ${cornerRadius}px")
    }

    /**
     * 清理资源 - 使用协程取消和资源清理
     */
    private fun cleanup() {
        MyLog.d(TAG, "清理资源")

        // 移除弹窗状态监听器
        DialogStateManager.removeStateChangeListener(this)
        MyLog.d(TAG, "已移除弹窗状态监听器")

        // 取消所有协程
        coroutineScope.cancel()

        // 清理状态
        sceneStates.clear()
        currentActiveScene = null
        currentMapView = null

        // 注意：shapeDrawable 使用 lazy 初始化，不需要手动清理
        // 注意：isActivityFocused 使用 AtomicBoolean，重置为 false
        isActivityFocused.set(false)

        MyLog.d(TAG, "资源清理完成")
    }

    /**
     * 获取调试信息 - 使用字符串模板和集合操作
     */
    fun getDebugInfo(): String = buildString {
        appendLine("SceneMapManagerKt状态:")
        appendLine("- Activity焦点: $isActivityFocused")
        appendLine("- 当前活跃Scene: $currentActiveScene")
        appendLine("- 注册Scene数量: ${sceneStates.size}")
        appendLine("- 导航软件类型: ${if (isBaiduNavigation()) "百度地图" else "高德地图/其他"}")
        appendLine("- 弹窗状态: ${if (DialogStateManager.hasActiveDialogs()) "有弹窗(${DialogStateManager.getActiveDialogCount()}个)" else "无弹窗"}")
        appendLine("- 地图功能状态: ${if (isMapFunctionDisabled) "已禁用" else "正常"}")

        sceneStates.forEach { (sceneId, state) ->
            appendLine("  - $sceneId: 可见=${state.isVisible}, 应显示地图=${state.shouldShowMap}, 导航Scene=${state.isNavigationScene}")
        }

        // 添加弹窗详细信息
        if (DialogStateManager.hasActiveDialogs()) {
            appendLine("弹窗详细信息:")
            appendLine(DialogStateManager.getDebugInfo())
        }

        // 添加循环检测信息
        appendLine(getLoopDetectionInfo())
    }

    // ==================== 百度地图适配相关方法 ====================

    /**
     * 检测当前是否使用百度导航
     * 通过检查默认导航应用的包名和应用名来判断
     *
     * @return true表示使用百度导航，false表示其他导航或未设置
     */
    private fun isBaiduNavigation(): Boolean {
        try {
            val defaultNavi = SettingsManager.getDefaultNavi()
            if (defaultNavi.isEmpty()) {
                MyLog.v(TAG, "未设置默认导航应用")
                return false
            }

            val appInfo = Gson().fromJson(defaultNavi, AppInfo::class.java)
            val packageName = appInfo.packageName?.lowercase() ?: ""
            val appName = appInfo.appName?.lowercase() ?: ""

            // 检查包名是否包含百度相关标识
            val isBaiduPackage = packageName.contains("baidu") ||
                               packageName.contains("baidumap") ||
                               packageName.contains("baidunavi")

            // 检查应用名是否包含百度相关标识
            val isBaiduApp = appName.contains("百度") ||
                           appName.contains("baidu")

            val result = isBaiduPackage || isBaiduApp

            MyLog.d(TAG, "导航软件检测 - 包名: $packageName, 应用名: $appName, 是否百度: $result")
            return result

        } catch (e: Exception) {
            MyLog.e(TAG, "检测导航软件类型时发生异常: ${e.message}")
            return false
        }
    }

    /**
     * 处理百度导航的焦点变化
     * 百度车机版地图会导致焦点频繁变化，需要特殊处理
     *
     * 策略：完全忽略Activity焦点变化事件
     * 百度地图的显示/隐藏将完全由Scene可见性和弹窗状态控制
     *
     * @param hasFocus 是否获得焦点
     */
    private fun handleBaiduNavigationFocusChange(hasFocus: Boolean) {
        MyLog.d(TAG, "百度导航焦点变化: $hasFocus - 忽略Activity焦点事件，使用Scene控制")
        // 对于百度导航，完全忽略Activity焦点变化
        // 地图显示/隐藏将由以下方式控制：
        // 1. Scene可见性变化 (onSceneVisibilityChanged)
        // 2. Scene地图显示设置 (setSceneShouldShowMap)
        // 3. 弹窗状态变化（如果实现了DialogStateManager）
    }

    /**
     * 处理标准导航（高德等）的焦点变化
     * 使用原有的标准逻辑
     *
     * @param hasFocus 是否获得焦点
     */
    private fun handleStandardNavigationFocusChange(hasFocus: Boolean) {
        if (hasFocus) {
            // 标准导航：获得焦点，延迟检查地图显示
            scheduleMapCheck("标准导航获得焦点")
        } else {
            // 标准导航：失去焦点，立即隐藏地图
            cancelMapCheck()
            hideMapInternal()
        }
    }

    /**
     * 安排延迟的地图检查 - 支持自定义延迟时间
     *
     * @param reason 检查原因
     * @param delayTime 延迟时间（毫秒），默认使用DELAY_CHECK_TIME
     */
    private fun scheduleMapCheckWithDelay(reason: String, delayTime: Long = DELAY_CHECK_TIME) {
        MyLog.d(TAG, "安排延迟地图检查: $reason (延迟${delayTime}ms)")

        // 取消之前的检查任务
        cancelMapCheck()

        // 使用协程延迟执行
        delayedCheckJob = coroutineScope.launch {
            delay(delayTime)
            MyLog.d(TAG, "执行延迟地图检查: $reason")
            checkAndUpdateMapDisplay()
        }
    }

    /**
     * 百度导航专用的地图显示检查
     * 不依赖Activity焦点状态，直接检查Scene和系统设置
     * 同时检查弹窗状态，如果有弹窗显示则隐藏地图
     * 特别处理View布局完成检查，避免位置为(0,0)的问题
     */
    private fun checkAndUpdateMapDisplayForBaidu() {
        MyLog.d(TAG, "百度导航模式 - 检查地图显示状态（忽略Activity焦点）")

        // 检查弹窗状态 - 如果有弹窗显示，立即隐藏地图
        if (DialogStateManager.hasActiveDialogs()) {
            MyLog.w(TAG, "有弹窗显示，隐藏地图 - 弹窗数量: ${DialogStateManager.getActiveDialogCount()}")
            hideMapInternal()
            return
        }

        val sceneId = currentActiveScene
        if (sceneId == null) {
            MyLog.w(TAG, "无活跃Scene")
            hideMapInternal()
            return
        }

        val state = sceneStates[sceneId]
        if (state == null) {
            MyLog.w(TAG, "Scene状态未找到: $sceneId")
            hideMapInternal()
            return
        }

        // 使用优化的条件检查方法
        if (!state.canShowMap()) {
            MyLog.w(TAG, "Scene条件不满足: ${state.getStatusSummary()}")
            hideMapInternal()
            return
        }

        // 检查系统设置
        if (!checkSystemSettings()) {
            MyLog.w(TAG, "系统设置不满足地图显示条件")
            hideMapInternal()
            return
        }

        // 所有条件满足，检查View布局状态后显示地图
        MyLog.i(TAG, "百度导航模式 - 条件满足，检查View布局后显示地图: $sceneId")
        val container = state.mapContainer // 创建本地副本避免智能转换问题
        container?.let { mapContainer ->
            showMapInternalWithLayoutCheck(mapContainer)
        }
    }

    /**
     * 带布局检查的地图显示方法
     * 确保View已完成布局后再显示地图，避免位置为(0,0)的问题
     */
    private fun showMapInternalWithLayoutCheck(mapViewContainer: View) {
        // 检查View是否已经完成布局
        if (mapViewContainer.width > 0 && mapViewContainer.height > 0) {
            // View已完成布局，直接显示地图
            MyLog.d(TAG, "View已完成布局，直接显示地图")
            showMapInternal(mapViewContainer)
        } else {
            // View还未完成布局，等待布局完成
            MyLog.d(TAG, "View未完成布局，等待布局完成后显示地图")
            mapViewContainer.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    // 移除监听器避免重复调用
                    mapViewContainer.viewTreeObserver.removeOnGlobalLayoutListener(this)

                    // 再次检查View尺寸
                    if (mapViewContainer.width > 0 && mapViewContainer.height > 0) {
                        MyLog.d(TAG, "布局完成，显示地图 - 尺寸: ${mapViewContainer.width}x${mapViewContainer.height}")
                        showMapInternal(mapViewContainer)
                    } else {
                        MyLog.w(TAG, "布局完成但View尺寸仍为0，延迟重试")
                        // 延迟重试
                        coroutineScope.launch {
                            delay(100)
                            if (mapViewContainer.width > 0 && mapViewContainer.height > 0) {
                                showMapInternal(mapViewContainer)
                            } else {
                                MyLog.e(TAG, "延迟重试后View尺寸仍为0，放弃显示地图")
                            }
                        }
                    }
                }
            })
        }
    }

    // ==================== DialogStateChangeListener 实现 ====================

    /**
     * 弹窗显示时的回调
     * 对于百度导航，需要立即隐藏地图避免被弹窗遮挡
     */
    override fun onDialogShown(dialogInfo: DialogStateManager.DialogInfo, totalCount: Int) {
        MyLog.d(TAG, "弹窗显示: ${dialogInfo.className} (${dialogInfo.type}), 总数: $totalCount")

        if (isBaiduNavigation()) {
            MyLog.d(TAG, "百度导航模式 - 弹窗显示时隐藏地图")
            hideMapInternal()
        } else {
            MyLog.d(TAG, "标准导航模式 - 弹窗显示时隐藏地图")
            hideMapInternal()
        }
    }

    /**
     * 弹窗隐藏时的回调
     * 对于百度导航，需要检查是否可以重新显示地图
     */
    override fun onDialogDismissed(dialogInfo: DialogStateManager.DialogInfo, totalCount: Int) {
        MyLog.d(TAG, "弹窗隐藏: ${dialogInfo.className} (${dialogInfo.type}), 剩余总数: $totalCount")

        // 如果还有其他弹窗显示，不恢复地图
        if (totalCount > 0) {
            MyLog.d(TAG, "还有其他弹窗显示，不恢复地图显示")
            return
        }

        if (isBaiduNavigation()) {
            MyLog.d(TAG, "百度导航模式 - 所有弹窗关闭，检查是否恢复地图显示")
            // 百度导航：直接检查地图显示条件
            checkAndUpdateMapDisplayForBaidu()
        } else {
            MyLog.d(TAG, "标准导航模式 - 所有弹窗关闭，延迟检查地图显示")
            // 标准导航：延迟检查地图显示
            scheduleMapCheck("弹窗关闭后恢复")
        }
    }

    // ==================== 循环检测和保护机制 ====================

    /**
     * 记录地图操作 - 线程安全
     */
    private fun recordMapOperation(type: OperationType, reason: String) {
        val currentTime = System.currentTimeMillis()
        val operation = MapOperation(type, currentTime, reason)

        // mapOperationHistory已经是线程安全的Collections.synchronizedList
        mapOperationHistory.add(operation)

        // 保持历史记录大小
        if (mapOperationHistory.size > OPERATION_HISTORY_SIZE) {
            mapOperationHistory.removeAt(0)
        }

        MyLog.v(TAG, "记录地图操作: $type - $reason")
    }

    /**
     * 检测是否存在循环 - 优化的智能检测逻辑，线程安全
     */
    private fun detectLoop(): Boolean {
        if (mapOperationHistory.size < LOOP_THRESHOLD) {
            return false
        }

        val currentTime = System.currentTimeMillis()
        // 创建快照避免并发修改
        val historySnapshot = mapOperationHistory.toList()
        val recentOperations = historySnapshot.filter {
            currentTime - it.timestamp <= LOOP_DETECTION_WINDOW
        }

        if (recentOperations.size >= LOOP_THRESHOLD) {
            // 检查是否有频繁的显示/隐藏切换
            val showCount = recentOperations.count { it.type == OperationType.SHOW }
            val hideCount = recentOperations.count { it.type == OperationType.HIDE }

            // 更严格的循环判断条件
            if (showCount >= 4 && hideCount >= 4) {
                // 检查是否存在真正的循环模式（连续的显示-隐藏-显示-隐藏）
                if (hasAlternatingPattern(recentOperations)) {
                    // 排除正常的业务场景
                    if (!isNormalBusinessScenario(recentOperations)) {
                        MyLog.e(TAG, "检测到地图显示/隐藏循环！${LOOP_DETECTION_WINDOW/1000}秒内执行了${recentOperations.size}次操作")
                        MyLog.e(TAG, "操作详情: 显示${showCount}次, 隐藏${hideCount}次")
                        logOperationDetails(recentOperations)
                        return true
                    } else {
                        MyLog.w(TAG, "检测到频繁操作但属于正常业务场景，不视为循环")
                    }
                }
            }
        }

        return false
    }

    /**
     * 检查是否存在交替模式（显示-隐藏-显示-隐藏）
     */
    private fun hasAlternatingPattern(operations: List<MapOperation>): Boolean {
        if (operations.size < 4) return false

        // 按时间排序
        val sortedOps = operations.sortedBy { it.timestamp }

        // 检查是否存在连续的交替模式
        var alternatingCount = 0
        for (i in 1 until sortedOps.size) {
            if (sortedOps[i].type != sortedOps[i-1].type) {
                alternatingCount++
            } else {
                alternatingCount = 0
            }

            // 连续4次交替就认为是循环模式
            if (alternatingCount >= 3) {
                return true
            }
        }

        return false
    }

    /**
     * 判断是否是正常的业务场景
     */
    private fun isNormalBusinessScenario(operations: List<MapOperation>): Boolean {
        val reasons = operations.map { it.reason }.distinct()

        // 正常场景的操作原因
        val normalReasons = setOf(
            "Scene可见", "设置地图显示状态", "弹窗显示时隐藏地图",
            "弹窗关闭后恢复", "布局完成", "Activity获得焦点", "Activity失去焦点"
        )

        // 如果所有操作都是正常业务原因，且没有重复的广播冲突特征，则认为是正常场景
        val hasNormalReasons = reasons.all { reason ->
            normalReasons.any { normalReason -> reason.contains(normalReason) }
        }

        // 检查时间间隔，如果操作间隔都很短（<50ms），可能是广播冲突
        val sortedOps = operations.sortedBy { it.timestamp }
        var shortIntervalCount = 0
        for (i in 1 until sortedOps.size) {
            val interval = sortedOps[i].timestamp - sortedOps[i-1].timestamp
            if (interval < 50) {
                shortIntervalCount++
            }
        }

        // 如果大部分操作间隔都很短，可能是广播冲突导致的循环
        val isLikelyBroadcastConflict = shortIntervalCount > operations.size * 0.7

        return hasNormalReasons && !isLikelyBroadcastConflict
    }

    /**
     * 记录操作详情用于调试
     */
    private fun logOperationDetails(operations: List<MapOperation>) {
        MyLog.e(TAG, "操作详情:")
        operations.sortedBy { it.timestamp }.forEach { op ->
            val timeAgo = (System.currentTimeMillis() - op.timestamp)
            MyLog.e(TAG, "  - ${op.type} (${timeAgo}ms前): ${op.reason}")
        }
    }

    /**
     * 禁用地图功能
     */
    private fun disableMapFunction() {
        isMapFunctionDisabled = true
        disableStartTime = System.currentTimeMillis()

        MyLog.e(TAG, "检测到地图循环操作，禁用地图功能${DISABLE_DURATION/1000}秒")
        MyLog.e(TAG, "可能原因：高德地图和百度地图同时运行，导致广播冲突")

        // 立即隐藏地图
        val intent = Intent("com.autonavi.plus.closemap")
        appContext.sendBroadcast(intent)
        currentMapView = null

        // 显示用户提示
        showUserNotification()

        // 清空操作历史
        synchronized(mapOperationHistory) {
            mapOperationHistory.clear()
        }
    }

    /**
     * 重新启用地图功能
     */
    private fun enableMapFunction() {
        isMapFunctionDisabled = false
        disableStartTime = 0L

        MyLog.i(TAG, "地图功能已重新启用")

        // 清空操作历史
        synchronized(mapOperationHistory) {
            mapOperationHistory.clear()
        }
    }

    /**
     * 手动重置循环检测状态（用于误判时的快速恢复）- 线程安全
     */
    fun resetLoopDetection() {
        mapOperationHistory.clear()

        if (isMapFunctionDisabled) {
            enableMapFunction()
            MyLog.w(TAG, "手动重置循环检测状态，地图功能已恢复")
        } else {
            MyLog.i(TAG, "手动清空循环检测历史")
        }
    }

    /**
     * 显示用户提示
     */
    private fun showUserNotification() {
        try {
            // 发送通知给用户
            MToast.makeTextLong("检测到地图频繁显示/隐藏，可能是多个导航应用冲突。已暂时禁用地图功能${DISABLE_DURATION/1000}秒，请关闭其中一个导航应用。")
            MyLog.w(TAG, "已发送地图冲突通知给用户")
        } catch (e: Exception) {
            MyLog.e(TAG, "发送用户通知失败: ${e.message}")
        }
    }

    /**
     * 获取循环检测状态信息
     */
    fun getLoopDetectionInfo(): String = buildString {
        appendLine("循环检测状态:")
        appendLine("- 地图功能状态: ${if (isMapFunctionDisabled) "已禁用" else "正常"}")

        if (isMapFunctionDisabled) {
            val remainingTime = (DISABLE_DURATION - (System.currentTimeMillis() - disableStartTime)) / 1000
            appendLine("- 剩余禁用时间: ${maxOf(0, remainingTime)}秒")
        }

        synchronized(mapOperationHistory) {
            appendLine("- 操作历史数量: ${mapOperationHistory.size}")
            if (mapOperationHistory.isNotEmpty()) {
                appendLine("- 最近操作:")
                mapOperationHistory.takeLast(5).forEach { operation ->
                    val timeAgo = (System.currentTimeMillis() - operation.timestamp) / 1000
                    appendLine("  - ${operation.type} (${timeAgo}秒前): ${operation.reason}")
                }
            }
        }
    }
}
