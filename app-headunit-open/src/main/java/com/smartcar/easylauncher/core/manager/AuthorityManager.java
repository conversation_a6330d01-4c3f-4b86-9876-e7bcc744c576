package com.smartcar.easylauncher.core.manager;

import com.smartcar.easylauncher.core.constants.AuthorityConstants;
import com.smartcar.easylauncher.shared.utils.cache.MMKVUtils;

public class AuthorityManager {

    /**
     * 设置数据版本号
     */
    public static void setDataVersion(int dataVersion) {
        MMKVUtils.put(AuthorityConstants.DATA_VERSION, dataVersion);
    }

    /**
     * 获取数据版本号
     */
    public static int getDataVersion() {
        return MMKVUtils.get(AuthorityConstants.DATA_VERSION, 0);
    }

    /**
     * 设置token
     */
    public static void setToken(String token) {
        MMKVUtils.put(AuthorityConstants.TOKEN, token);
    }

    /**
     * 获取token
     */
    public static String getToken() {
        return MMKVUtils.get(AuthorityConstants.TOKEN, "");
    }


    /**
     * 是否通过使用许可
     */
    public static void setUseLicense(boolean useLicense) {
        MMKVUtils.put(AuthorityConstants.USE_LICENSE, useLicense);
    }

    /**
     * 获取是否通过使用许可
     */

    public static boolean getUseLicense() {
        return MMKVUtils.get(AuthorityConstants.USE_LICENSE, false);
    }


    /**
     * 激活码
     *
     * @param activationCode
     */
    public static void setActivationCode(String activationCode) {
        MMKVUtils.put(AuthorityConstants.ACTIVATION_CODE, activationCode);
    }

    /**
     * 获取激活码
     */
    public static String getActivationCode() {
        return MMKVUtils.get(AuthorityConstants.ACTIVATION_CODE, AuthorityConstants.RNtJWECIDFvfCFTR);
    }

    /**
     * 激活状态
     */
    public static void setActivationState(boolean state) {
        MMKVUtils.put(AuthorityConstants.ACTIVATION_STATE, state);
    }

    /**
     * 获取激活状态
     */

    public static boolean getActivationState() {
        return MMKVUtils.get(AuthorityConstants.ACTIVATION_STATE, false);
    }


    /**
     * AMAPKEY
     */
    public static void setAmapKey(String key) {
        MMKVUtils.put(AuthorityConstants.AMAP_KEY, key);
    }

    /**
     * AMAPKEY
     */

    public static String getAmapKey() {
        return MMKVUtils.get(AuthorityConstants.AMAP_KEY, AuthorityConstants.BB3FAA78DFFA);
    }

    /**
     * 存储OpenWeatherappId
     *
     * @param appId
     */
    public static void setOpenWeatherAPPId(String appId) {
        MMKVUtils.put(AuthorityConstants.OPEN_WEATHER_APP_ID, appId);
    }

    /**
     * 获取OpenWeatherappId
     */
    public static String getOpenWeatherAPPId() {
        return MMKVUtils.get(AuthorityConstants.OPEN_WEATHER_APP_ID, AuthorityConstants.dsaihfabglan);
    }

    //使用时长
    public static void setUseTime(long time) {
        MMKVUtils.put(AuthorityConstants.USE_TIME, time);
    }

    //使用时长
    public static long getUseTime() {
        return MMKVUtils.get(AuthorityConstants.USE_TIME, 0);
    }

}
