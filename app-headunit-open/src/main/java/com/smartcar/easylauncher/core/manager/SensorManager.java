package com.smartcar.easylauncher.core.manager;

import android.content.Context;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.os.Handler;
import android.os.Looper;

import com.smartcar.easylauncher.infrastructure.event.cody.SensorScopeBus;
import com.smartcar.easylauncher.data.model.system.SensorModel;
import com.smartcar.easylauncher.shared.utils.MyLog;

import static android.content.Context.SENSOR_SERVICE;

/**
 * 传感器管理器
 * <p>
 * 集中管理各种传感器的初始化、注册和注销，使用事件总线分发传感器数据
 * 避免在不同界面中重复初始化传感器，提高性能和电池效率
 * </p>
 */
public class SensorManager {
    private static final String TAG = "SensorManager";
    private static volatile SensorManager instance;

    private Context context;
    private android.hardware.SensorManager nativeSensorManager;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    // 传感器和监听器
    private Sensor orientationSensor;
    private OrientationSensorListener orientationListener;

    // 传感器刷新率
    private static final int SENSOR_DELAY = android.hardware.SensorManager.SENSOR_DELAY_GAME;

    // 是否已注册标志
    private boolean isOrientationRegistered = false;

    /**
     * 私有构造函数
     *
     * @param context 应用上下文
     */
    private SensorManager(Context context) {
        this.context = context.getApplicationContext();
        this.nativeSensorManager = (android.hardware.SensorManager) this.context.getSystemService(SENSOR_SERVICE);
        initSensors();
    }

    /**
     * 获取单例实例
     *
     * @param context 上下文
     * @return SensorManager实例
     */
    public static SensorManager getInstance(Context context) {
        if (instance == null) {
            synchronized (SensorManager.class) {
                if (instance == null) {
                    instance = new SensorManager(context);
                }
            }
        }
        return instance;
    }

    /**
     * 初始化传感器对象和监听器
     */
    private void initSensors() {
        // 初始化方向传感器
        orientationSensor = nativeSensorManager.getDefaultSensor(Sensor.TYPE_ORIENTATION);
        if (orientationSensor != null) {
            orientationListener = new OrientationSensorListener();
            MyLog.v(TAG, "方向传感器初始化成功");
        } else {
            MyLog.e(TAG, "设备不支持方向传感器");
            // 通知传感器不可用
            notifySensorUnavailable(SensorModel.TYPE_ORIENTATION);
        }
    }
    
    /**
     * 通知传感器不可用
     */
    private void notifySensorUnavailable(int sensorType) {
        mainHandler.post(() -> {
            SensorModel unavailableModel = SensorModel.createUnavailableSensor(
                sensorType, 
                System.currentTimeMillis()
            );
            SensorScopeBus.eventBean().post(unavailableModel);
        });
    }

    /**
     * 注册方向传感器监听器
     *
     * @return 是否注册成功
     */
    public boolean registerOrientationSensor() {
        if (nativeSensorManager == null || orientationSensor == null || orientationListener == null) {
            MyLog.e(TAG, "方向传感器不可用，无法注册");
            notifySensorUnavailable(SensorModel.TYPE_ORIENTATION);
            return false;
        }

        if (!isOrientationRegistered) {
            isOrientationRegistered = nativeSensorManager.registerListener(
                    orientationListener,
                    orientationSensor,
                    SENSOR_DELAY
            );
            MyLog.v(TAG, "方向传感器注册" + (isOrientationRegistered ? "成功" : "失败"));
            
            if (!isOrientationRegistered) {
                notifySensorUnavailable(SensorModel.TYPE_ORIENTATION);
            }
        }
        
        return isOrientationRegistered;
    }

    /**
     * 注销方向传感器监听器
     */
    public void unregisterOrientationSensor() {
        if (nativeSensorManager != null && orientationListener != null && isOrientationRegistered) {
            nativeSensorManager.unregisterListener(orientationListener, orientationSensor);
            isOrientationRegistered = false;
            MyLog.v(TAG, "方向传感器注销成功");
        }
    }

    /**
     * 检查是否有特定类型的传感器
     *
     * @param sensorType 传感器类型（来自Sensor.TYPE_*常量）
     * @return 是否存在该传感器
     */
    public boolean hasSensor(int sensorType) {
        return nativeSensorManager != null && nativeSensorManager.getDefaultSensor(sensorType) != null;
    }

    /**
     * 检查是否支持方向传感器
     *
     * @return 是否支持方向传感器
     */
    public boolean hasOrientationSensor() {
        return orientationSensor != null;
    }

    /**
     * 方向传感器监听器
     * 处理方向传感器数据并通过事件总线发送
     */
    private class OrientationSensorListener implements SensorEventListener {
        @Override
        public void onSensorChanged(SensorEvent event) {
            if (event.sensor.getType() == Sensor.TYPE_ORIENTATION) {
                // 获取方向传感器的方位角（以度为单位，范围从0到359.9度）
                // 0/360表示北，90表示东，180表示南，270表示西
                float azimuth = event.values[0];

                // 创建传感器数据模型
                SensorModel model = SensorModel.createOrientationData(
                        azimuth,
                        event.accuracy,
                        event.timestamp
                );

                // 通过事件总线发送方向数据
                mainHandler.post(() -> {
                    SensorScopeBus.eventBean().post(model);
                });
            }
        }

        @Override
        public void onAccuracyChanged(Sensor sensor, int accuracy) {
            if (sensor.getType() == Sensor.TYPE_ORIENTATION) {
                String accuracyStatus = getAccuracyDescription(accuracy);
                MyLog.v(TAG, "方向传感器精度变化: " + accuracyStatus);
            }
        }

        /**
         * 获取传感器精度描述
         * @param accuracy 精度值
         * @return 精度描述文本
         */
        private String getAccuracyDescription(int accuracy) {
            switch (accuracy) {
                case android.hardware.SensorManager.SENSOR_STATUS_ACCURACY_HIGH:
                    return "高精度";
                case android.hardware.SensorManager.SENSOR_STATUS_ACCURACY_MEDIUM:
                    return "中等精度";
                case android.hardware.SensorManager.SENSOR_STATUS_ACCURACY_LOW:
                    return "低精度";
                case android.hardware.SensorManager.SENSOR_STATUS_UNRELIABLE:
                    return "不可靠";
                default:
                    return "未知状态";
            }
        }
    }

    /**
     * 释放资源
     * 在应用退出或不再需要时调用
     */
    public void release() {
        unregisterOrientationSensor();
        orientationListener = null;
        nativeSensorManager = null;
        context = null;
        synchronized (SensorManager.class) {
            instance = null;
        }
    }
} 