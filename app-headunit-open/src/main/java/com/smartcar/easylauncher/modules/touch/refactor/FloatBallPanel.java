package com.smartcar.easylauncher.modules.touch.refactor;

import android.content.Context;
import android.graphics.PixelFormat;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 悬浮球面板
 * 显示操作按钮的面板
 * 
 * <AUTHOR>
 */
public class FloatBallPanel extends LinearLayout {
    
    private static final String TAG = "FloatBallPanel";
    
    // 操作常量
    public static final String ACTION_BACK = "back";
    public static final String ACTION_HOME = "home";
    public static final String ACTION_RECENT = "recent";
    public static final String ACTION_NOTIFICATION = "notification";
    
    private Context mContext;
    private WindowManager mWindowManager;
    private WindowManager.LayoutParams mLayoutParams;
    private boolean mIsShowing = false;
    
    private OnPanelListener mListener;
    
    public FloatBallPanel(Context context) {
        super(context);
        mContext = context;
        mWindowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        initView();
    }
    
    /**
     * 初始化视图
     */
    private void initView() {
        setOrientation(HORIZONTAL);
        
        // 加载布局
        LayoutInflater.from(mContext).inflate(R.layout.float_ball_panel, this, true);
        
        // 设置按钮点击事件
        findViewById(R.id.btn_back).setOnClickListener(v -> {
            if (mListener != null) {
                mListener.onActionClick(ACTION_BACK);
            }
        });
        
        findViewById(R.id.btn_home).setOnClickListener(v -> {
            if (mListener != null) {
                mListener.onActionClick(ACTION_HOME);
            }
        });
        
        findViewById(R.id.btn_recent).setOnClickListener(v -> {
            if (mListener != null) {
                mListener.onActionClick(ACTION_RECENT);
            }
        });
        
        findViewById(R.id.btn_notification).setOnClickListener(v -> {
            if (mListener != null) {
                mListener.onActionClick(ACTION_NOTIFICATION);
            }
        });
        
        // 设置背景点击事件（点击空白区域关闭面板）
        setOnClickListener(v -> {
            if (mListener != null) {
                mListener.onPanelDismiss();
            }
        });
    }
    
    /**
     * 显示面板
     */
    public void show(int buttonX, int buttonY) {
        if (mIsShowing) {
            return;
        }
        
        try {
            createLayoutParams(buttonX, buttonY);
            mWindowManager.addView(this, mLayoutParams);
            mIsShowing = true;
            MyLog.d(TAG, "面板显示成功");
        } catch (Exception e) {
            MyLog.e(TAG, "面板显示失败: " + e.getMessage());
        }
    }
    
    /**
     * 隐藏面板
     */
    public void hide() {
        if (!mIsShowing) {
            return;
        }
        
        try {
            mWindowManager.removeView(this);
            mIsShowing = false;
            MyLog.d(TAG, "面板隐藏成功");
        } catch (Exception e) {
            MyLog.e(TAG, "面板隐藏失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新位置
     */
    public void updatePosition(int buttonX, int buttonY) {
        if (!mIsShowing) {
            return;
        }
        
        calculatePosition(buttonX, buttonY);
        mWindowManager.updateViewLayout(this, mLayoutParams);
    }
    
    /**
     * 是否正在显示
     */
    public boolean isShowing() {
        return mIsShowing;
    }
    
    /**
     * 创建布局参数
     */
    private void createLayoutParams(int buttonX, int buttonY) {
        mLayoutParams = new WindowManager.LayoutParams();
        
        // 设置窗口类型
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            mLayoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            mLayoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        }
        
        // 设置窗口标志
        mLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL;
        
        // 设置像素格式
        mLayoutParams.format = PixelFormat.TRANSLUCENT;
        
        // 设置位置和大小
        mLayoutParams.gravity = Gravity.TOP | Gravity.START;
        mLayoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
        mLayoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        
        calculatePosition(buttonX, buttonY);
    }
    
    /**
     * 计算面板位置
     */
    private void calculatePosition(int buttonX, int buttonY) {
        int screenWidth = FloatBallUtils.getScreenWidth(mContext);
        int screenHeight = FloatBallUtils.getScreenHeight(mContext);
        
        int buttonSize = FloatBallUtils.dp2px(mContext, 60);
        int panelWidth = FloatBallUtils.dp2px(mContext, 240); // 预估面板宽度
        int panelHeight = FloatBallUtils.dp2px(mContext, 60); // 预估面板高度
        
        // 默认显示在悬浮球右侧
        int panelX = buttonX + buttonSize + FloatBallUtils.dp2px(mContext, 10);
        int panelY = buttonY;
        
        // 检查是否超出屏幕右边界
        if (panelX + panelWidth > screenWidth) {
            // 显示在悬浮球左侧
            panelX = buttonX - panelWidth - FloatBallUtils.dp2px(mContext, 10);
            FloatBallConfig.setIsLeft(true);
        } else {
            FloatBallConfig.setIsLeft(false);
        }
        
        // 检查是否超出屏幕下边界
        if (panelY + panelHeight > screenHeight) {
            panelY = screenHeight - panelHeight - FloatBallUtils.dp2px(mContext, 10);
        }
        
        // 检查是否超出屏幕上边界
        if (panelY < 0) {
            panelY = FloatBallUtils.dp2px(mContext, 10);
        }
        
        // 检查是否超出屏幕左边界
        if (panelX < 0) {
            panelX = FloatBallUtils.dp2px(mContext, 10);
        }
        
        mLayoutParams.x = panelX;
        mLayoutParams.y = panelY;
        
        // 保存面板位置
        FloatBallConfig.setPanelPosition(panelX, panelY);
    }
    
    /**
     * 设置监听器
     */
    public void setOnPanelListener(OnPanelListener listener) {
        mListener = listener;
    }
    
    /**
     * 面板监听接口
     */
    public interface OnPanelListener {
        void onActionClick(String action);
        void onPanelDismiss();
    }
}
