package com.smartcar.easylauncher.modules.touch.refactor;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;

import androidx.annotation.Nullable;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 悬浮球服务
 * 简化的后台服务，用于保活悬浮球功能
 * 
 * <AUTHOR>
 */
public class FloatBallService extends Service {
    
    private static final String TAG = "FloatBallService";
    private static final int NOTIFICATION_ID = 1001;
    private static final String CHANNEL_ID = "float_ball_channel";
    
    // 服务动作
    public static final String ACTION_START = "action_start";
    public static final String ACTION_STOP = "action_stop";
    public static final String ACTION_SHOW = "action_show";
    public static final String ACTION_HIDE = "action_hide";
    
    private FloatBallManager mFloatBallManager;
    
    @Override
    public void onCreate() {
        super.onCreate();
        MyLog.d(TAG, "服务创建");
        
        // 创建通知渠道
        createNotificationChannel();
        
        // 获取悬浮球管理器
        mFloatBallManager = FloatBallManager.getInstance(this);
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        MyLog.d(TAG, "服务启动");
        
        if (intent != null) {
            String action = intent.getAction();
            handleAction(action);
        }
        
        // 启动前台服务
        startForeground(NOTIFICATION_ID, createNotification());
        
        // 返回START_STICKY，确保服务被杀死后能自动重启
        return START_STICKY;
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        MyLog.d(TAG, "服务销毁");
        
        // 隐藏悬浮球
        if (mFloatBallManager != null) {
            mFloatBallManager.hide();
        }
    }
    
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
    
    /**
     * 处理服务动作
     */
    private void handleAction(String action) {
        if (action == null) {
            return;
        }
        
        MyLog.d(TAG, "处理动作: " + action);
        
        switch (action) {
            case ACTION_START:
                handleStart();
                break;
            case ACTION_STOP:
                handleStop();
                break;
            case ACTION_SHOW:
                handleShow();
                break;
            case ACTION_HIDE:
                handleHide();
                break;
            default:
                MyLog.w(TAG, "未知动作: " + action);
                break;
        }
    }
    
    /**
     * 处理启动
     */
    private void handleStart() {
        MyLog.d(TAG, "启动悬浮球服务");
        
        // 如果配置中启用了悬浮球，则显示
        if (FloatBallConfig.isEnabled()) {
            if (mFloatBallManager != null) {
                mFloatBallManager.show();
            }
        }
    }
    
    /**
     * 处理停止
     */
    private void handleStop() {
        MyLog.d(TAG, "停止悬浮球服务");
        
        // 隐藏悬浮球
        if (mFloatBallManager != null) {
            mFloatBallManager.hide();
        }
        
        // 停止服务
        stopSelf();
    }
    
    /**
     * 处理显示
     */
    private void handleShow() {
        MyLog.d(TAG, "显示悬浮球");
        
        if (mFloatBallManager != null) {
            mFloatBallManager.show();
        }
    }
    
    /**
     * 处理隐藏
     */
    private void handleHide() {
        MyLog.d(TAG, "隐藏悬浮球");
        
        if (mFloatBallManager != null) {
            mFloatBallManager.hide();
        }
    }
    
    /**
     * 创建通知渠道
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "悬浮球服务",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("悬浮球后台服务通知");
            channel.setShowBadge(false);
            
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }
    
    /**
     * 创建通知
     */
    private Notification createNotification() {
        Notification.Builder builder;
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            builder = new Notification.Builder(this, CHANNEL_ID);
        } else {
            builder = new Notification.Builder(this);
        }
        
        return builder
                .setContentTitle("悬浮球服务")
                .setContentText("悬浮球服务正在运行")
                .setSmallIcon(R.mipmap.ic_launcher)
                .setOngoing(true)
                .setAutoCancel(false)
                .build();
    }
}
