package com.smartcar.easylauncher.modules.touch.refactor;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;

import androidx.appcompat.widget.AppCompatImageView;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 悬浮球视图
 * 集成了拖拽、点击、动画等所有UI交互功能
 * 
 * <AUTHOR>
 */
public class FloatBallView extends AppCompatImageView {
    
    private static final String TAG = "FloatBallView";
    
    // 状态常量
    public static final int STATE_NORMAL = 0;
    public static final int STATE_EXPANDED = 1;
    
    // 动画常量
    private static final long ANIMATION_DURATION = 250;
    private static final float SCALE_NORMAL = 1.0f;
    private static final float SCALE_EXPANDED = 0.8f;
    private static final float ALPHA_NORMAL = 1.0f;
    private static final float ALPHA_DRAGGING = 0.8f;
    
    // 拖拽相关
    private static final int CLICK_THRESHOLD = 10; // 点击阈值
    private static final long LONG_PRESS_TIMEOUT = 500; // 长按超时
    
    private WindowManager mWindowManager;
    private WindowManager.LayoutParams mLayoutParams;
    private Context mContext;
    
    // 状态变量
    private int mCurrentState = STATE_NORMAL;
    private boolean mIsDragging = false;
    private boolean mIsLongPressed = false;
    
    // 拖拽相关变量
    private float mDownX, mDownY;
    private float mLastX, mLastY;
    private int mStartX, mStartY;
    
    // 动画
    private AnimatorSet mCurrentAnimator;
    
    // 回调接口
    private OnFloatBallListener mListener;
    
    // Handler
    private Handler mHandler = new Handler(Looper.getMainLooper());
    
    // 长按检测任务
    private Runnable mLongPressRunnable = new Runnable() {
        @Override
        public void run() {
            if (!mIsDragging) {
                mIsLongPressed = true;
                FloatBallUtils.vibrate(mContext);
                if (mListener != null) {
                    mListener.onLongPress();
                }
            }
        }
    };
    
    public FloatBallView(Context context) {
        super(context);
        init(context);
    }
    
    public FloatBallView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }
    
    public FloatBallView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }
    
    private void init(Context context) {
        mContext = context;
        mWindowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        
        // 设置默认图标
        setImageResource(R.drawable.ic_float);
        setBackgroundResource(R.drawable.float_icon_bg);
        setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        
        // 设置点击监听
        setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return handleTouchEvent(event);
            }
        });
    }
    
    /**
     * 显示悬浮球
     */
    public void show() {
        if (getParent() != null) {
            return; // 已经显示
        }
        
        try {
            createLayoutParams();
            mWindowManager.addView(this, mLayoutParams);
            MyLog.d(TAG, "悬浮球显示成功");
        } catch (Exception e) {
            MyLog.e(TAG, "悬浮球显示失败: " + e.getMessage());
        }
    }
    
    /**
     * 隐藏悬浮球
     */
    public void hide() {
        if (getParent() == null) {
            return; // 已经隐藏
        }
        
        try {
            mWindowManager.removeView(this);
            MyLog.d(TAG, "悬浮球隐藏成功");
        } catch (Exception e) {
            MyLog.e(TAG, "悬浮球隐藏失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建布局参数
     */
    private void createLayoutParams() {
        mLayoutParams = new WindowManager.LayoutParams();
        
        // 设置窗口类型
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            mLayoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            mLayoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        }
        
        // 设置窗口标志
        mLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;
        
        // 设置像素格式
        mLayoutParams.format = PixelFormat.TRANSLUCENT;
        
        // 设置位置和大小
        mLayoutParams.gravity = Gravity.TOP | Gravity.START;
        mLayoutParams.width = FloatBallUtils.dp2px(mContext, 60);
        mLayoutParams.height = FloatBallUtils.dp2px(mContext, 60);
        mLayoutParams.x = FloatBallConfig.getButtonX();
        mLayoutParams.y = FloatBallConfig.getButtonY();
    }
    
    /**
     * 处理触摸事件
     */
    private boolean handleTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                return handleActionDown(event);
            case MotionEvent.ACTION_MOVE:
                return handleActionMove(event);
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                return handleActionUp(event);
        }
        return false;
    }
    
    private boolean handleActionDown(MotionEvent event) {
        mDownX = event.getRawX();
        mDownY = event.getRawY();
        mLastX = mDownX;
        mLastY = mDownY;
        mStartX = mLayoutParams.x;
        mStartY = mLayoutParams.y;
        
        mIsDragging = false;
        mIsLongPressed = false;
        
        // 开始长按检测
        mHandler.postDelayed(mLongPressRunnable, LONG_PRESS_TIMEOUT);
        
        return true;
    }
    
    private boolean handleActionMove(MotionEvent event) {
        float currentX = event.getRawX();
        float currentY = event.getRawY();
        
        float deltaX = currentX - mDownX;
        float deltaY = currentY - mDownY;
        
        // 判断是否开始拖拽
        if (!mIsDragging && (Math.abs(deltaX) > CLICK_THRESHOLD || Math.abs(deltaY) > CLICK_THRESHOLD)) {
            mIsDragging = true;
            mHandler.removeCallbacks(mLongPressRunnable);
            
            // 拖拽开始
            setAlpha(ALPHA_DRAGGING);
            FloatBallUtils.vibrate(mContext);
            
            if (mListener != null) {
                mListener.onDragStart();
            }
        }
        
        if (mIsDragging) {
            // 更新位置
            mLayoutParams.x = mStartX + (int) deltaX;
            mLayoutParams.y = mStartY + (int) deltaY;
            
            // 边界检查
            int[] fixedPos = FloatBallUtils.fixPositionInScreen(mContext, 
                mLayoutParams.x, mLayoutParams.y, mLayoutParams.width, mLayoutParams.height);
            mLayoutParams.x = fixedPos[0];
            mLayoutParams.y = fixedPos[1];
            
            mWindowManager.updateViewLayout(this, mLayoutParams);
            
            if (mListener != null) {
                mListener.onDragging(mLayoutParams.x, mLayoutParams.y);
            }
        }
        
        mLastX = currentX;
        mLastY = currentY;
        return true;
    }
    
    private boolean handleActionUp(MotionEvent event) {
        mHandler.removeCallbacks(mLongPressRunnable);
        
        if (mIsDragging) {
            // 拖拽结束
            mIsDragging = false;
            setAlpha(ALPHA_NORMAL);
            
            // 保存位置
            FloatBallConfig.setButtonPosition(mLayoutParams.x, mLayoutParams.y);
            
            if (mListener != null) {
                mListener.onDragEnd(mLayoutParams.x, mLayoutParams.y);
            }
        } else if (!mIsLongPressed) {
            // 点击事件
            if (mListener != null) {
                mListener.onClick();
            }
        }
        
        return true;
    }
    
    /**
     * 切换状态
     */
    public void toggleState() {
        if (mCurrentState == STATE_NORMAL) {
            expandState();
        } else {
            normalState();
        }
    }
    
    /**
     * 展开状态
     */
    public void expandState() {
        if (mCurrentState == STATE_EXPANDED) return;
        
        mCurrentState = STATE_EXPANDED;
        setSelected(true);
        
        startAnimation(SCALE_EXPANDED, ALPHA_NORMAL);
        
        if (mListener != null) {
            mListener.onStateChanged(STATE_EXPANDED);
        }
    }
    
    /**
     * 正常状态
     */
    public void normalState() {
        if (mCurrentState == STATE_NORMAL) return;
        
        mCurrentState = STATE_NORMAL;
        setSelected(false);
        
        startAnimation(SCALE_NORMAL, ALPHA_NORMAL);
        
        if (mListener != null) {
            mListener.onStateChanged(STATE_NORMAL);
        }
    }
    
    /**
     * 开始动画
     */
    private void startAnimation(float scale, float alpha) {
        if (mCurrentAnimator != null && mCurrentAnimator.isRunning()) {
            mCurrentAnimator.cancel();
        }
        
        mCurrentAnimator = new AnimatorSet();
        ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(this, View.SCALE_X, scale);
        ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(this, View.SCALE_Y, scale);
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(this, View.ALPHA, alpha);
        
        mCurrentAnimator.playTogether(scaleXAnimator, scaleYAnimator, alphaAnimator);
        mCurrentAnimator.setDuration(ANIMATION_DURATION);
        mCurrentAnimator.start();
    }
    
    /**
     * 获取当前状态
     */
    public int getCurrentState() {
        return mCurrentState;
    }
    
    /**
     * 设置监听器
     */
    public void setOnFloatBallListener(OnFloatBallListener listener) {
        mListener = listener;
    }
    
    /**
     * 悬浮球监听接口
     */
    public interface OnFloatBallListener {
        void onClick();
        void onLongPress();
        void onDragStart();
        void onDragging(int x, int y);
        void onDragEnd(int x, int y);
        void onStateChanged(int newState);
    }
}
