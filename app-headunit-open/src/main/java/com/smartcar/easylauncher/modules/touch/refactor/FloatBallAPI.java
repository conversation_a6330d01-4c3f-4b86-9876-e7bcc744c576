package com.smartcar.easylauncher.modules.touch.refactor;

import android.content.Context;

import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 悬浮球API接口
 * 提供简洁的外部调用接口，替代原来复杂的调用方式
 * 
 * <AUTHOR>
 */
public class FloatBallAPI {
    
    private static final String TAG = "FloatBallAPI";
    
    // ==================== 基础控制API ====================
    
    /**
     * 显示悬浮球
     * 
     * @param context 上下文
     */
    public static void show(Context context) {
        try {
            FloatBallManager manager = FloatBallManager.getInstance(context);
            manager.show();
            MyLog.i(TAG, "API调用：显示悬浮球");
        } catch (Exception e) {
            MyLog.e(TAG, "显示悬浮球失败: " + e.getMessage());
        }
    }
    
    /**
     * 隐藏悬浮球
     * 
     * @param context 上下文
     */
    public static void hide(Context context) {
        try {
            FloatBallManager manager = FloatBallManager.getInstance(context);
            manager.hide();
            MyLog.i(TAG, "API调用：隐藏悬浮球");
        } catch (Exception e) {
            MyLog.e(TAG, "隐藏悬浮球失败: " + e.getMessage());
        }
    }
    
    /**
     * 切换悬浮球显示状态
     * 
     * @param context 上下文
     */
    public static void toggle(Context context) {
        try {
            FloatBallManager manager = FloatBallManager.getInstance(context);
            manager.toggle();
            MyLog.i(TAG, "API调用：切换悬浮球状态");
        } catch (Exception e) {
            MyLog.e(TAG, "切换悬浮球状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查悬浮球是否正在显示
     * 
     * @param context 上下文
     * @return 是否正在显示
     */
    public static boolean isShowing(Context context) {
        try {
            FloatBallManager manager = FloatBallManager.getInstance(context);
            return manager.isShowing();
        } catch (Exception e) {
            MyLog.e(TAG, "检查悬浮球状态失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 销毁悬浮球
     * 
     * @param context 上下文
     */
    public static void destroy(Context context) {
        try {
            FloatBallManager manager = FloatBallManager.getInstance(context);
            manager.destroy();
            MyLog.i(TAG, "API调用：销毁悬浮球");
        } catch (Exception e) {
            MyLog.e(TAG, "销毁悬浮球失败: " + e.getMessage());
        }
    }
    
    // ==================== 配置API ====================
    
    /**
     * 设置悬浮球是否启用
     * 
     * @param enabled 是否启用
     */
    public static void setEnabled(boolean enabled) {
        FloatBallConfig.setEnabled(enabled);
        MyLog.i(TAG, "API调用：设置悬浮球启用状态 = " + enabled);
    }
    
    /**
     * 获取悬浮球是否启用
     * 
     * @return 是否启用
     */
    public static boolean isEnabled() {
        return FloatBallConfig.isEnabled();
    }
    
    /**
     * 设置悬浮球模式
     * 
     * @param mode 模式值
     */
    public static void setMode(int mode) {
        FloatBallConfig.setMode(mode);
        MyLog.i(TAG, "API调用：设置悬浮球模式 = " + mode);
    }
    
    /**
     * 获取悬浮球模式
     * 
     * @return 模式值
     */
    public static int getMode() {
        return FloatBallConfig.getMode();
    }
    
    /**
     * 设置悬浮球位置
     * 
     * @param x X坐标
     * @param y Y坐标
     */
    public static void setPosition(int x, int y) {
        FloatBallConfig.setButtonPosition(x, y);
        MyLog.i(TAG, "API调用：设置悬浮球位置 = (" + x + ", " + y + ")");
    }
    
    /**
     * 获取悬浮球X坐标
     * 
     * @return X坐标
     */
    public static int getPositionX() {
        return FloatBallConfig.getButtonX();
    }
    
    /**
     * 获取悬浮球Y坐标
     * 
     * @return Y坐标
     */
    public static int getPositionY() {
        return FloatBallConfig.getButtonY();
    }
    
    /**
     * 重置配置为默认值
     */
    public static void resetToDefault() {
        FloatBallConfig.resetToDefault();
        MyLog.i(TAG, "API调用：重置悬浮球配置");
    }
    
    // ==================== 权限API ====================
    
    /**
     * 检查悬浮窗权限
     * 
     * @param context 上下文
     * @return 是否有权限
     */
    public static boolean hasPermission(Context context) {
        return FloatBallUtils.hasFloatPermission(context);
    }
    
    /**
     * 请求悬浮窗权限
     * 
     * @param context 上下文
     */
    public static void requestPermission(Context context) {
        FloatBallUtils.requestFloatPermission(context);
        MyLog.i(TAG, "API调用：请求悬浮窗权限");
    }
    
    // ==================== 调试API ====================
    
    /**
     * 获取配置摘要信息
     * 
     * @return 配置摘要
     */
    public static String getConfigSummary() {
        return FloatBallConfig.getConfigSummary();
    }
    
    /**
     * 打印调试信息
     */
    public static void printDebugInfo() {
        MyLog.d(TAG, "=== 悬浮球调试信息 ===");
        MyLog.d(TAG, getConfigSummary());
        MyLog.d(TAG, "===================");
    }
}
