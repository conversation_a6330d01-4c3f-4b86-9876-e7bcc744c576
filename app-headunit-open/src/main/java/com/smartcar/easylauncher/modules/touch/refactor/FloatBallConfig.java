package com.smartcar.easylauncher.modules.touch.refactor;

import android.content.Context;

import com.smartcar.easylauncher.app.App;
import com.smartcar.easylauncher.modules.touch.util.ScreenUtil;
import com.smartcar.easylauncher.shared.utils.cache.MMKVUtils;

/**
 * 悬浮球配置管理类
 * 统一管理悬浮球的所有配置项，替代原来复杂的 FloatManager
 * 
 * <AUTHOR>
 */
public class FloatBallConfig {
    
    // 配置键名常量
    private static final String KEY_ENABLED = "float_ball_enabled";
    private static final String KEY_BUTTON_X = "float_ball_button_x";
    private static final String KEY_BUTTON_Y = "float_ball_button_y";
    private static final String KEY_PANEL_X = "float_ball_panel_x";
    private static final String KEY_PANEL_Y = "float_ball_panel_y";
    private static final String KEY_IS_LEFT = "float_ball_is_left";
    private static final String KEY_MODE = "float_ball_mode";
    private static final String KEY_CUSTOM_MENU = "float_ball_custom_menu";
    
    // 默认值常量
    private static final float DEFAULT_X_RATIO = 0.8f;
    private static final float DEFAULT_Y_RATIO = 0.3f;
    private static final int DEFAULT_MODE = 0;
    
    private static Context mContext = App.getContextInstance();
    
    // ==================== 基础配置 ====================
    
    /**
     * 设置悬浮球是否启用
     */
    public static void setEnabled(boolean enabled) {
        MMKVUtils.putBoolean(KEY_ENABLED, enabled);
    }
    
    /**
     * 获取悬浮球是否启用
     */
    public static boolean isEnabled() {
        return MMKVUtils.getBoolean(KEY_ENABLED, false);
    }
    
    /**
     * 设置悬浮球模式
     */
    public static void setMode(int mode) {
        MMKVUtils.put(KEY_MODE, mode);
    }
    
    /**
     * 获取悬浮球模式
     */
    public static int getMode() {
        return MMKVUtils.get(KEY_MODE, DEFAULT_MODE);
    }
    
    // ==================== 位置配置 ====================
    
    /**
     * 设置悬浮球按钮位置
     */
    public static void setButtonPosition(int x, int y) {
        MMKVUtils.put(KEY_BUTTON_X, x);
        MMKVUtils.put(KEY_BUTTON_Y, y);
    }
    
    /**
     * 获取悬浮球按钮X坐标
     */
    public static int getButtonX() {
        return MMKVUtils.get(KEY_BUTTON_X, 
            ScreenUtil.getPointFromScreenWidthRatio(mContext, DEFAULT_X_RATIO));
    }
    
    /**
     * 获取悬浮球按钮Y坐标
     */
    public static int getButtonY() {
        return MMKVUtils.get(KEY_BUTTON_Y, 
            ScreenUtil.getPointFromScreenHeightRatio(mContext, DEFAULT_Y_RATIO));
    }
    
    /**
     * 设置悬浮面板位置
     */
    public static void setPanelPosition(int x, int y) {
        MMKVUtils.put(KEY_PANEL_X, x);
        MMKVUtils.put(KEY_PANEL_Y, y);
    }
    
    /**
     * 获取悬浮面板X坐标
     */
    public static int getPanelX() {
        return MMKVUtils.get(KEY_PANEL_X, 0);
    }
    
    /**
     * 获取悬浮面板Y坐标
     */
    public static int getPanelY() {
        return MMKVUtils.get(KEY_PANEL_Y, 0);
    }
    
    /**
     * 设置悬浮窗是否在左边
     */
    public static void setIsLeft(boolean isLeft) {
        MMKVUtils.putBoolean(KEY_IS_LEFT, isLeft);
    }
    
    /**
     * 获取悬浮窗是否在左边
     */
    public static boolean isLeft() {
        return MMKVUtils.getBoolean(KEY_IS_LEFT, false);
    }
    
    // ==================== 自定义菜单配置 ====================
    
    /**
     * 设置自定义菜单数据
     */
    public static void setCustomMenuData(String menuData) {
        MMKVUtils.put(KEY_CUSTOM_MENU, menuData);
    }
    
    /**
     * 获取自定义菜单数据
     */
    public static String getCustomMenuData() {
        return MMKVUtils.get(KEY_CUSTOM_MENU, "");
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 重置为默认配置
     */
    public static void resetToDefault() {
        setEnabled(false);
        setMode(DEFAULT_MODE);
        setButtonPosition(
            ScreenUtil.getPointFromScreenWidthRatio(mContext, DEFAULT_X_RATIO),
            ScreenUtil.getPointFromScreenHeightRatio(mContext, DEFAULT_Y_RATIO)
        );
        setPanelPosition(0, 0);
        setIsLeft(false);
        setCustomMenuData("");
    }
    
    /**
     * 获取配置摘要信息（用于调试）
     */
    public static String getConfigSummary() {
        return String.format(
            "FloatBall Config: enabled=%s, mode=%d, buttonPos=(%d,%d), panelPos=(%d,%d), isLeft=%s",
            isEnabled(), getMode(), getButtonX(), getButtonY(), 
            getPanelX(), getPanelY(), isLeft()
        );
    }
}
