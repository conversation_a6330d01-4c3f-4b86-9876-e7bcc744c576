package com.smartcar.easylauncher.modules.touch.refactor;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Vibrator;
import android.provider.Settings;
import android.view.KeyEvent;

import com.smartcar.easylauncher.home.main.HomeActivity;
import com.smartcar.easylauncher.shared.utils.music.MusicControllerTools;
import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 悬浮球工具类
 * 合并原来的多个工具类功能
 * 
 * <AUTHOR>
 */
public class FloatBallUtils {
    
    private static final String TAG = "FloatBallUtils";
    
    // ==================== 权限相关 ====================
    
    /**
     * 检查悬浮窗权限
     */
    public static boolean hasFloatPermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return Settings.canDrawOverlays(context);
        }
        return true;
    }
    
    /**
     * 请求悬浮窗权限
     */
    public static void requestFloatPermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        }
    }
    
    // ==================== 系统操作 ====================
    
    /**
     * 执行返回操作
     */
    public static void performBack() {
        try {
            MusicControllerTools.sendKeyEvent(KeyEvent.KEYCODE_BACK);
            MyLog.d(TAG, "执行返回操作");
        } catch (Exception e) {
            MyLog.e(TAG, "返回操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行Home操作
     */
    public static void performHome(Context context) {
        try {
            Intent homeIntent = new Intent(context, HomeActivity.class);
            homeIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            context.startActivity(homeIntent);
            MyLog.d(TAG, "执行Home操作");
        } catch (Exception e) {
            MyLog.e(TAG, "Home操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行任务管理操作
     */
    public static void performRecentTasks() {
        try {
            MusicControllerTools.sendKeyEvent(KeyEvent.KEYCODE_APP_SWITCH);
            MyLog.d(TAG, "执行任务管理操作");
        } catch (Exception e) {
            MyLog.e(TAG, "任务管理操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 下拉通知栏
     */
    public static void pullDownNotificationBar(Context context) {
        try {
            // 这里可以使用反射或其他方式实现
            MyLog.d(TAG, "下拉通知栏");
        } catch (Exception e) {
            MyLog.e(TAG, "下拉通知栏失败: " + e.getMessage());
        }
    }
    
    // ==================== 震动反馈 ====================
    
    /**
     * 执行震动反馈
     */
    public static void vibrate(Context context) {
        vibrate(context, 50); // 默认震动50毫秒
    }
    
    /**
     * 执行指定时长的震动
     */
    public static void vibrate(Context context, long duration) {
        try {
            Vibrator vibrator = (Vibrator) context.getSystemService(Context.VIBRATOR_SERVICE);
            if (vibrator != null && vibrator.hasVibrator()) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    vibrator.vibrate(android.os.VibrationEffect.createOneShot(duration, 
                        android.os.VibrationEffect.DEFAULT_AMPLITUDE));
                } else {
                    vibrator.vibrate(duration);
                }
            }
        } catch (Exception e) {
            MyLog.e(TAG, "震动失败: " + e.getMessage());
        }
    }
    
    // ==================== 屏幕相关 ====================
    
    /**
     * 获取屏幕宽度
     */
    public static int getScreenWidth(Context context) {
        return context.getResources().getDisplayMetrics().widthPixels;
    }
    
    /**
     * 获取屏幕高度
     */
    public static int getScreenHeight(Context context) {
        return context.getResources().getDisplayMetrics().heightPixels;
    }
    
    /**
     * dp转px
     */
    public static int dp2px(Context context, float dpValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dpValue * scale + 0.5f);
    }
    
    /**
     * px转dp
     */
    public static int px2dp(Context context, float pxValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (pxValue / scale + 0.5f);
    }
    
    /**
     * 根据屏幕宽度比例获取坐标点
     */
    public static int getPointFromScreenWidthRatio(Context context, float ratio) {
        return (int) (getScreenWidth(context) * ratio);
    }
    
    /**
     * 根据屏幕高度比例获取坐标点
     */
    public static int getPointFromScreenHeightRatio(Context context, float ratio) {
        return (int) (getScreenHeight(context) * ratio);
    }
    
    // ==================== 边界检查 ====================
    
    /**
     * 检查坐标是否在屏幕范围内
     */
    public static boolean isInScreenBounds(Context context, int x, int y, int width, int height) {
        int screenWidth = getScreenWidth(context);
        int screenHeight = getScreenHeight(context);
        
        return x >= 0 && y >= 0 && 
               (x + width) <= screenWidth && 
               (y + height) <= screenHeight;
    }
    
    /**
     * 修正坐标到屏幕范围内
     */
    public static int[] fixPositionInScreen(Context context, int x, int y, int width, int height) {
        int screenWidth = getScreenWidth(context);
        int screenHeight = getScreenHeight(context);
        
        // 修正X坐标
        if (x < 0) x = 0;
        if (x + width > screenWidth) x = screenWidth - width;
        
        // 修正Y坐标
        if (y < 0) y = 0;
        if (y + height > screenHeight) y = screenHeight - height;
        
        return new int[]{x, y};
    }
    
    // ==================== 服务控制 ====================
    
    /**
     * 启动前台服务
     */
    public static void startForegroundService(Context context, Intent intent) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
    }
}
